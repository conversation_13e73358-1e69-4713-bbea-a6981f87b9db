import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  Upload,
  Calendar,
  Building2,
  Brain,
  Users,
  X,
  Bell,
  TrendingUp,
  BarChart3
} from 'lucide-react';

interface FloatingNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  category: 'import' | 'reservation' | 'logement' | 'ia' | 'user' | 'system' | 'navigation';
  timestamp: Date;
  autoHide?: boolean;
  duration?: number;
}

interface FloatingNotificationsProps {
  className?: string;
}

export const FloatingNotifications: React.FC<FloatingNotificationsProps> = ({ className = '' }) => {
  const [notifications, setNotifications] = useState<FloatingNotification[]>([]);

  // Fonction pour ajouter une notification
  const addNotification = (notification: Omit<FloatingNotification, 'id' | 'timestamp'>) => {
    const newNotification: FloatingNotification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      autoHide: notification.autoHide ?? true,
      duration: notification.duration ?? 4000
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Garder max 5 notifications

    // Auto-hide si activé
    if (newNotification.autoHide) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, newNotification.duration);
    }
  };

  // Fonction pour supprimer une notification
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Simuler des notifications automatiques
  useEffect(() => {
    const interval = setInterval(() => {
      const randomNotifications = [
        {
          type: 'info' as const,
          title: 'Synchronisation automatique',
          message: 'Les données ont été synchronisées avec la base',
          category: 'system' as const
        },
        {
          type: 'success' as const,
          title: 'Nouvelle réservation détectée',
          message: 'Une réservation a été ajoutée automatiquement',
          category: 'reservation' as const
        },
        {
          type: 'info' as const,
          title: 'Analyse IA mise à jour',
          message: 'Les prédictions ont été recalculées',
          category: 'ia' as const
        }
      ];

      // Ajouter une notification aléatoire toutes les 30 secondes
      if (Math.random() > 0.7) {
        const randomNotif = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
        addNotification(randomNotif);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const getIcon = (type: string, category: string) => {
    if (type === 'success') return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (type === 'error') return <AlertCircle className="w-4 h-4 text-red-500" />;
    if (type === 'warning') return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    
    switch (category) {
      case 'import': return <Upload className="w-4 h-4 text-blue-500" />;
      case 'reservation': return <Calendar className="w-4 h-4 text-indigo-500" />;
      case 'logement': return <Building2 className="w-4 h-4 text-emerald-500" />;
      case 'ia': return <Brain className="w-4 h-4 text-pink-500" />;
      case 'user': return <Users className="w-4 h-4 text-orange-500" />;
      case 'navigation': return <TrendingUp className="w-4 h-4 text-blue-500" />;
      default: return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';
      case 'error': return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      default: return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
    }
  };

  const getCategoryEmoji = (category: string) => {
    switch (category) {
      case 'import': return '📥';
      case 'reservation': return '📋';
      case 'logement': return '🏠';
      case 'ia': return '🤖';
      case 'user': return '👥';
      case 'navigation': return '📊';
      default: return '🔔';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div className={`fixed bottom-4 right-4 z-40 space-y-2 ${className}`}>
      {notifications.map((notification, index) => (
        <Card
          key={notification.id}
          className={`
            w-80 border shadow-lg backdrop-blur-sm
            ${getBackgroundColor(notification.type)}
            transform transition-all duration-300 ease-in-out
            ${index === 0 ? 'scale-100' : 'scale-95'}
            hover:scale-105
          `}
          style={{
            transform: `translateY(${index * -10}px)`,
            zIndex: 40 - index
          }}
        >
          <div className="p-3">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 mt-0.5">
                {getIcon(notification.type, notification.category)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm">{getCategoryEmoji(notification.category)}</span>
                  <h4 className="font-semibold text-xs text-slate-800 dark:text-slate-200 truncate">
                    {notification.title}
                  </h4>
                </div>
                
                <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                  {notification.message}
                </p>
                
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-slate-500 dark:text-slate-500">
                    {notification.timestamp.toLocaleTimeString('fr-FR', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeNotification(notification.id)}
                className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 p-1 h-auto"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Hook pour utiliser les notifications flottantes
export const useFloatingNotifications = () => {
  const [notificationComponent, setNotificationComponent] = useState<React.ReactNode>(null);

  useEffect(() => {
    setNotificationComponent(<FloatingNotifications />);
  }, []);

  const showFloatingNotification = (notification: Omit<FloatingNotification, 'id' | 'timestamp'>) => {
    // Cette fonction sera connectée au composant via un contexte ou un state global
    console.log('Floating notification:', notification);
  };

  return {
    notificationComponent,
    showFloatingNotification
  };
};

// Notifications prédéfinies pour les actions courantes
export const NotificationTemplates = {
  import: {
    start: {
      type: 'info' as const,
      title: 'Import démarré',
      message: 'Traitement du fichier en cours...',
      category: 'import' as const
    },
    success: (count: number) => ({
      type: 'success' as const,
      title: 'Import réussi',
      message: `${count} éléments importés avec succès`,
      category: 'import' as const
    }),
    error: (error: string) => ({
      type: 'error' as const,
      title: 'Erreur d\'import',
      message: error,
      category: 'import' as const
    })
  },
  
  reservation: {
    created: {
      type: 'success' as const,
      title: 'Réservation créée',
      message: 'Nouvelle réservation ajoutée avec succès',
      category: 'reservation' as const
    },
    deleted: {
      type: 'success' as const,
      title: 'Réservation supprimée',
      message: 'Réservation supprimée et analyses mises à jour',
      category: 'reservation' as const
    }
  },
  
  logement: {
    created: {
      type: 'success' as const,
      title: 'Logement créé',
      message: 'Nouveau logement ajouté avec succès',
      category: 'logement' as const
    },
    autoCreated: (name: string) => ({
      type: 'info' as const,
      title: 'Logement auto-créé',
      message: `"${name}" créé automatiquement lors de l'import`,
      category: 'logement' as const
    })
  },
  
  ia: {
    updated: {
      type: 'info' as const,
      title: 'IA mise à jour',
      message: 'Analyses et prédictions recalculées',
      category: 'ia' as const
    },
    analysisComplete: {
      type: 'success' as const,
      title: 'Analyse terminée',
      message: 'Nouvelle analyse IA générée avec succès',
      category: 'ia' as const
    }
  },
  
  navigation: {
    sectionChanged: (section: string) => ({
      type: 'info' as const,
      title: 'Navigation',
      message: `Section "${section}" chargée`,
      category: 'navigation' as const,
      duration: 2000
    })
  }
};
