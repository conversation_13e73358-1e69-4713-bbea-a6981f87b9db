import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Upload, Trash2, Image as ImageIcon } from 'lucide-react';
import { 
  useAddPhotosMutation, 
  useDeletePhotoMutation,
  Logement 
} from '@/features/api/logementsApi';

interface PhotoModalProps {
  isOpen: boolean;
  onClose: () => void;
  logement: Logement | null;
}

export const PhotoModal: React.FC<PhotoModalProps> = ({
  isOpen,
  onClose,
  logement
}) => {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  const [addPhotos, { isLoading: isUploading }] = useAddPhotosMutation();
  const [deletePhoto, { isLoading: isDeleting }] = useDeletePhotoMutation();

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFiles(e.target.files);
  };

  const handleUpload = async () => {
    if (!selectedFiles || !logement) return;

    try {
      await addPhotos({ id: logement.id, photos: selectedFiles }).unwrap();
      toast({
        title: "Succès",
        description: "Photos ajoutées avec succès",
      });
      setSelectedFiles(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.data?.message || "Erreur lors de l'upload",
        variant: "destructive",
      });
    }
  };

  const handleDeletePhoto = async (photoIndex: number) => {
    if (!logement) return;

    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette photo ?')) {
      try {
        await deletePhoto({ id: logement.id, photoIndex }).unwrap();
        toast({
          title: "Succès",
          description: "Photo supprimée avec succès",
        });
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.data?.message || "Erreur lors de la suppression",
          variant: "destructive",
        });
      }
    }
  };

  if (!logement) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Gestion des photos - {logement.nom_logement}</DialogTitle>
          <DialogDescription>
            Ajoutez ou supprimez des photos pour ce logement.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload de nouvelles photos */}
          <div className="space-y-4">
            <Label>Ajouter de nouvelles photos</Label>
            <div className="flex items-center gap-4">
              <Input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                className="flex-1"
              />
              <Button 
                onClick={handleUpload}
                disabled={!selectedFiles || isUploading}
                className="gap-2"
              >
                <Upload className="w-4 h-4" />
                {isUploading ? 'Upload...' : 'Ajouter'}
              </Button>
            </div>
            {selectedFiles && (
              <p className="text-sm text-muted-foreground">
                {selectedFiles.length} fichier(s) sélectionné(s)
              </p>
            )}
          </div>

          {/* Photos existantes */}
          <div className="space-y-4">
            <Label>Photos existantes</Label>
            {logement.photos && logement.photos.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {logement.photos.map((photo, index) => (
                  <div key={index} className="relative group">
                    <div className="aspect-square bg-muted rounded-lg overflow-hidden">
                      <img
                        src={`http://localhost:3000/uploads/photos/${photo}`}
                        alt={`Photo ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback si l'image ne charge pas
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                      <div className="hidden w-full h-full flex items-center justify-center">
                        <ImageIcon className="w-8 h-8 text-muted-foreground" />
                      </div>
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => handleDeletePhoto(index)}
                      disabled={isDeleting}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <ImageIcon className="w-12 h-12 mx-auto mb-2" />
                <p>Aucune photo pour ce logement</p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Fermer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
