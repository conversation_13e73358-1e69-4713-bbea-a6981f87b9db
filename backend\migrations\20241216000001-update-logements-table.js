'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('logements');

    // Ajouter les nouvelles colonnes seulement si elles n'existent pas
    if (!tableDescription.adresse) {
      await queryInterface.addColumn('logements', 'adresse', {
        type: Sequelize.STRING(500),
        allowNull: true
      });
    }

    if (!tableDescription.ville) {
      await queryInterface.addColumn('logements', 'ville', {
        type: Sequelize.STRING(100),
        allowNull: true
      });
    }

    if (!tableDescription.capacite) {
      await queryInterface.addColumn('logements', 'capacite', {
        type: Sequelize.INTEGER,
        allowNull: true
      });
    }

    if (!tableDescription.statut) {
      await queryInterface.addColumn('logements', 'statut', {
        type: Sequelize.STRING(50),
        allowNull: true,
        defaultValue: 'Actif'
      });
    }

    if (!tableDescription.description) {
      await queryInterface.addColumn('logements', 'description', {
        type: Sequelize.TEXT,
        allowNull: true
      });
    }

    if (!tableDescription.photos) {
      await queryInterface.addColumn('logements', 'photos', {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      });
    }

    if (!tableDescription.created_at) {
      await queryInterface.addColumn('logements', 'created_at', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: Sequelize.NOW
      });
    }

    if (!tableDescription.updated_at) {
      await queryInterface.addColumn('logements', 'updated_at', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: Sequelize.NOW
      });
    }

    // Mettre à jour les valeurs NULL avec la date actuelle
    await queryInterface.sequelize.query(`
      UPDATE logements
      SET created_at = COALESCE(created_at, NOW()),
          updated_at = COALESCE(updated_at, NOW())
    `);

    // Maintenant rendre les colonnes NOT NULL si elles viennent d'être créées
    if (!tableDescription.created_at) {
      await queryInterface.changeColumn('logements', 'created_at', {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      });
    }

    if (!tableDescription.updated_at) {
      await queryInterface.changeColumn('logements', 'updated_at', {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      });
    }

    // Ajouter une contrainte de validation pour la plateforme
    await queryInterface.addConstraint('logements', {
      fields: ['plateforme'],
      type: 'check',
      name: 'logements_plateforme_check',
      where: {
        plateforme: {
          [Sequelize.Op.in]: ['airbnb', 'booking']
        }
      }
    });

    // Ajouter une contrainte de validation pour le statut
    await queryInterface.addConstraint('logements', {
      fields: ['statut'],
      type: 'check',
      name: 'logements_statut_check',
      where: {
        statut: {
          [Sequelize.Op.in]: ['Actif', 'Inactif', 'Maintenance']
        }
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Supprimer les contraintes
    await queryInterface.removeConstraint('logements', 'logements_statut_check');
    await queryInterface.removeConstraint('logements', 'logements_plateforme_check');

    // Supprimer les colonnes ajoutées
    await queryInterface.removeColumn('logements', 'updated_at');
    await queryInterface.removeColumn('logements', 'created_at');
    await queryInterface.removeColumn('logements', 'photos');
    await queryInterface.removeColumn('logements', 'description');
    await queryInterface.removeColumn('logements', 'statut');
    await queryInterface.removeColumn('logements', 'capacite');
    await queryInterface.removeColumn('logements', 'ville');
    await queryInterface.removeColumn('logements', 'adresse');
  }
};
