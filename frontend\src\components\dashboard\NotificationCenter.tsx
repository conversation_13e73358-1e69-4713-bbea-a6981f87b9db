import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Bell, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  Upload,
  Trash2,
  RefreshCw,
  Calendar,
  Building2,
  Brain,
  Users
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  action?: string;
  read: boolean;
  category: 'import' | 'reservation' | 'logement' | 'ia' | 'user' | 'system';
}

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ isOpen, onClose }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const { toast } = useToast();

  // Simuler des notifications initiales
  useEffect(() => {
    const initialNotifications: Notification[] = [
      {
        id: '1',
        type: 'success',
        title: 'Import réussi',
        message: 'Fichier "reservations_janvier.csv" importé avec succès. 25 réservations ajoutées.',
        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        action: 'import',
        read: false,
        category: 'import'
      },
      {
        id: '2',
        type: 'info',
        title: 'Nouveau logement détecté',
        message: 'Le logement "Villa Marrakech" a été créé automatiquement lors de l\'importation.',
        timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
        action: 'auto-create',
        read: false,
        category: 'logement'
      },
      {
        id: '3',
        type: 'warning',
        title: 'Analyse IA mise à jour',
        message: 'Les prédictions ont été recalculées suite aux nouvelles données.',
        timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
        action: 'ai-update',
        read: true,
        category: 'ia'
      },
      {
        id: '4',
        type: 'success',
        title: 'Réservation supprimée',
        message: 'La réservation de Jean Dupont a été supprimée. Les statistiques ont été mises à jour.',
        timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        action: 'delete',
        read: true,
        category: 'reservation'
      }
    ];
    setNotifications(initialNotifications);
  }, []);

  const getNotificationIcon = (type: string, category: string) => {
    if (type === 'success') return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (type === 'error') return <AlertCircle className="w-5 h-5 text-red-500" />;
    if (type === 'warning') return <AlertCircle className="w-5 h-5 text-yellow-500" />;
    
    // Icônes par catégorie
    switch (category) {
      case 'import': return <Upload className="w-5 h-5 text-blue-500" />;
      case 'reservation': return <Calendar className="w-5 h-5 text-indigo-500" />;
      case 'logement': return <Building2 className="w-5 h-5 text-emerald-500" />;
      case 'ia': return <Brain className="w-5 h-5 text-pink-500" />;
      case 'user': return <Users className="w-5 h-5 text-orange-500" />;
      default: return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'border-l-green-500 bg-green-50 dark:bg-green-900/20';
      case 'error': return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'warning': return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      default: return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'import': return 'Import';
      case 'reservation': return 'Réservation';
      case 'logement': return 'Logement';
      case 'ia': return 'IA';
      case 'user': return 'Utilisateur';
      default: return 'Système';
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
    toast({
      title: "✅ Notifications marquées comme lues",
      description: "Toutes les notifications ont été marquées comme lues",
    });
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
    toast({
      title: "🗑️ Notification supprimée",
      description: "La notification a été supprimée",
    });
  };

  const clearAllNotifications = () => {
    setNotifications([]);
    toast({
      title: "🧹 Notifications effacées",
      description: "Toutes les notifications ont été supprimées",
    });
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-start justify-end p-4">
      <Card className="w-full max-w-md h-[80vh] bg-white dark:bg-slate-800 shadow-2xl rounded-2xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="w-6 h-6" />
              <CardTitle className="text-lg font-semibold">
                Centre de Notifications
              </CardTitle>
              {unreadCount > 0 && (
                <Badge className="bg-red-500 text-white">
                  {unreadCount}
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/20 rounded-full p-2"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <div className="p-4 border-b border-slate-200 dark:border-slate-700">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
              className="flex-1 text-xs"
            >
              <CheckCircle className="w-3 h-3 mr-1" />
              Tout marquer lu
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllNotifications}
              disabled={notifications.length === 0}
              className="flex-1 text-xs"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Tout effacer
            </Button>
          </div>
        </div>

        <CardContent className="p-0 overflow-y-auto flex-1">
          {notifications.length === 0 ? (
            <div className="p-8 text-center">
              <Bell className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-500 dark:text-slate-400">
                Aucune notification
              </p>
              <p className="text-sm text-slate-400 dark:text-slate-500 mt-1">
                Les nouvelles notifications apparaîtront ici
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 border-l-4 ${getNotificationColor(notification.type)} ${
                    !notification.read ? 'border-r-4 border-r-blue-500' : ''
                  } hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors cursor-pointer`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex items-start gap-3 flex-1">
                      {getNotificationIcon(notification.type, notification.category)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-sm text-slate-800 dark:text-slate-200 truncate">
                            {notification.title}
                          </h4>
                          <Badge variant="outline" className="text-xs">
                            {getCategoryLabel(notification.category)}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                          {notification.message}
                        </p>
                        <p className="text-xs text-slate-500 dark:text-slate-500 mt-2">
                          {notification.timestamp.toLocaleTimeString('fr-FR', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })} • {notification.timestamp.toLocaleDateString('fr-FR')}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteNotification(notification.id);
                      }}
                      className="text-slate-400 hover:text-red-500 p-1"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Hook pour ajouter des notifications
export const useNotificationCenter = () => {
  const { toast } = useToast();

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    // Pour l'instant, on utilise les toasts
    // Plus tard, on pourra intégrer avec un state global
    toast({
      title: notification.title,
      description: notification.message,
      variant: notification.type === 'error' ? 'destructive' : 'default',
    });
  };

  return { addNotification };
};
