const { Logement, Reservation, sequelize } = require('../models');
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs').promises;

// Récupérer tous les logements avec leurs statistiques
exports.getAllLogements = async (req, res) => {
  try {
    const { plateforme, search, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;
    const where = {};

    console.log('Paramètres reçus:', { plateforme, search, page, limit });

    if (plateforme && plateforme !== 'all') {
      where.plateforme = plateforme;
      console.log('Filtre plateforme appliqué:', plateforme);
    }

    if (search && search.trim()) {
      where[Op.or] = [
        {
          nom_logement: {
            [Op.iLike]: `%${search.trim()}%`
          }
        },
        {
          ville: {
            [Op.iLike]: `%${search.trim()}%`
          }
        }
      ];
      console.log('Filtre recherche appliqué:', search.trim());
    }

    console.log('Conditions WHERE:', JSON.stringify(where, null, 2));

    const { count, rows } = await Logement.findAndCountAll({
      where,
      include: [
        {
          model: Reservation,
          as: 'reservations',
          attributes: [],
          required: false
        }
      ],
      attributes: [
        'id',
        'nom_logement',
        'plateforme',
        'adresse',
        'ville',
        'capacite',
        'statut',
        'photos',
        'description',
        'created_at',
        'updated_at',
        [
          sequelize.fn('COUNT', sequelize.col('reservations.id')),
          'total_reservations'
        ],
        [
          sequelize.fn('COALESCE', sequelize.fn('SUM', sequelize.col('reservations.montant_total')), 0),
          'total_revenus'
        ]
      ],
      group: ['Logement.id'],
      order: [['nom_logement', 'ASC']],
      offset: parseInt(offset),
      limit: parseInt(limit),
      subQuery: false
    });

    console.log(`Trouvé ${rows.length} logements sur ${count.length} total`);

    // Convertir les agrégations en nombres pour éviter les problèmes d'affichage
    const processedRows = rows.map(row => {
      const logement = row.toJSON ? row.toJSON() : row;
      return {
        ...logement,
        total_reservations: parseInt(logement.total_reservations) || 0,
        total_revenus: parseFloat(logement.total_revenus) || 0
      };
    });

    res.json({
      success: true,
      data: processedRows,
      pagination: {
        total: count.length,
        page: parseInt(page),
        pages: Math.ceil(count.length / limit),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des logements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Récupérer un logement par ID avec ses réservations
exports.getLogementById = async (req, res) => {
  try {
    const { id } = req.params;

    const logement = await Logement.findByPk(id, {
      include: [
        {
          model: Reservation,
          as: 'reservations',
          order: [['date_arrivee', 'DESC']],
          limit: 10
        }
      ]
    });

    if (!logement) {
      return res.status(404).json({
        success: false,
        message: 'Logement non trouvé'
      });
    }

    res.json({
      success: true,
      data: logement
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du logement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Créer un nouveau logement
exports.createLogement = async (req, res) => {
  try {
    const {
      nom_logement,
      plateforme,
      adresse,
      ville,
      capacite,
      description,
      statut = 'Actif'
    } = req.body;

    // Vérifier si le logement existe déjà
    const existingLogement = await Logement.findOne({
      where: {
        nom_logement,
        plateforme
      }
    });

    if (existingLogement) {
      return res.status(400).json({
        success: false,
        message: 'Un logement avec ce nom existe déjà sur cette plateforme'
      });
    }

    const logement = await Logement.create({
      nom_logement,
      plateforme,
      adresse,
      ville,
      capacite: capacite ? parseInt(capacite) : null,
      description,
      statut,
      photos: []
    });

    res.status(201).json({
      success: true,
      message: 'Logement créé avec succès',
      data: logement
    });
  } catch (error) {
    console.error('Erreur lors de la création du logement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Mettre à jour un logement
exports.updateLogement = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      nom_logement,
      plateforme,
      adresse,
      ville,
      capacite,
      description,
      statut
    } = req.body;

    console.log('Mise à jour logement ID:', id);
    console.log('Données reçues:', req.body);

    const logement = await Logement.findByPk(id);

    if (!logement) {
      return res.status(404).json({
        success: false,
        message: 'Logement non trouvé'
      });
    }

    console.log('Logement trouvé:', logement.toJSON());

    // Vérifier si le nouveau nom n'existe pas déjà (sauf pour ce logement)
    if (nom_logement && nom_logement.trim() !== logement.nom_logement) {
      const existingLogement = await Logement.findOne({
        where: {
          nom_logement: nom_logement.trim(),
          plateforme: plateforme || logement.plateforme,
          id: { [Op.ne]: id }
        }
      });

      if (existingLogement) {
        return res.status(400).json({
          success: false,
          message: 'Un logement avec ce nom existe déjà sur cette plateforme'
        });
      }
    }

    // Préparer les données de mise à jour
    const updateData = {
      nom_logement: nom_logement ? nom_logement.trim() : logement.nom_logement,
      plateforme: plateforme || logement.plateforme,
      adresse: adresse !== undefined ? (adresse ? adresse.trim() : null) : logement.adresse,
      ville: ville !== undefined ? (ville ? ville.trim() : null) : logement.ville,
      capacite: capacite !== undefined ? (capacite ? parseInt(capacite) : null) : logement.capacite,
      description: description !== undefined ? (description ? description.trim() : null) : logement.description,
      statut: statut || logement.statut,
      updated_at: new Date()
    };

    console.log('Données de mise à jour:', updateData);

    await logement.update(updateData);

    // Récupérer le logement mis à jour
    const updatedLogement = await Logement.findByPk(id);

    console.log('Logement mis à jour:', updatedLogement.toJSON());

    res.json({
      success: true,
      message: 'Logement mis à jour avec succès',
      data: updatedLogement
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du logement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Supprimer un logement
exports.deleteLogement = async (req, res) => {
  try {
    const { id } = req.params;

    const logement = await Logement.findByPk(id, {
      include: [
        {
          model: Reservation,
          as: 'reservations'
        }
      ]
    });

    if (!logement) {
      return res.status(404).json({
        success: false,
        message: 'Logement non trouvé'
      });
    }

    // Vérifier s'il y a des réservations associées
    if (logement.reservations && logement.reservations.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Impossible de supprimer ce logement car il a des réservations associées'
      });
    }

    // Supprimer les photos associées
    if (logement.photos && logement.photos.length > 0) {
      for (const photo of logement.photos) {
        try {
          await fs.unlink(path.join('uploads', photo));
        } catch (err) {
          console.warn('Erreur lors de la suppression de la photo:', err.message);
        }
      }
    }

    await logement.destroy();

    res.json({
      success: true,
      message: 'Logement supprimé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du logement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Ajouter des photos à un logement
exports.addPhotos = async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Aucune photo fournie'
      });
    }

    const logement = await Logement.findByPk(id);

    if (!logement) {
      return res.status(404).json({
        success: false,
        message: 'Logement non trouvé'
      });
    }

    const currentPhotos = logement.photos || [];
    const newPhotos = req.files.map(file => file.filename);
    const updatedPhotos = [...currentPhotos, ...newPhotos];

    await logement.update({
      photos: updatedPhotos
    });

    res.json({
      success: true,
      message: 'Photos ajoutées avec succès',
      data: {
        logement,
        newPhotos: newPhotos.map(photo => `/uploads/${photo}`)
      }
    });
  } catch (error) {
    console.error('Erreur lors de l\'ajout des photos:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Supprimer une photo d'un logement
exports.deletePhoto = async (req, res) => {
  try {
    const { id, photoIndex } = req.params;

    const logement = await Logement.findByPk(id);

    if (!logement) {
      return res.status(404).json({
        success: false,
        message: 'Logement non trouvé'
      });
    }

    const photos = logement.photos || [];
    const index = parseInt(photoIndex);

    if (index < 0 || index >= photos.length) {
      return res.status(400).json({
        success: false,
        message: 'Index de photo invalide'
      });
    }

    const photoToDelete = photos[index];

    // Supprimer le fichier physique
    try {
      await fs.unlink(path.join('uploads', photoToDelete));
    } catch (err) {
      console.warn('Erreur lors de la suppression du fichier photo:', err.message);
    }

    // Mettre à jour la liste des photos
    const updatedPhotos = photos.filter((_, i) => i !== index);

    await logement.update({
      photos: updatedPhotos
    });

    res.json({
      success: true,
      message: 'Photo supprimée avec succès',
      data: logement
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de la photo:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtenir les statistiques d'un logement
exports.getLogementStats = async (req, res) => {
  try {
    const { id } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    const logement = await Logement.findByPk(id);

    if (!logement) {
      return res.status(404).json({
        success: false,
        message: 'Logement non trouvé'
      });
    }

    // Statistiques des réservations
    const reservationStats = await Reservation.findAll({
      where: {
        logement_id: id,
        date_arrivee: {
          [Op.gte]: new Date(`${year}-01-01`),
          [Op.lt]: new Date(`${parseInt(year) + 1}-01-01`)
        }
      },
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total_reservations'],
        [sequelize.fn('SUM', sequelize.col('montant_total')), 'total_revenus'],
        [sequelize.fn('AVG', sequelize.col('montant_total')), 'revenu_moyen'],
        [sequelize.fn('SUM', sequelize.col('nb_nuits')), 'total_nuits']
      ],
      raw: true
    });

    // Réservations par mois
    const monthlyStats = await Reservation.findAll({
      where: {
        logement_id: id,
        date_arrivee: {
          [Op.gte]: new Date(`${year}-01-01`),
          [Op.lt]: new Date(`${parseInt(year) + 1}-01-01`)
        }
      },
      attributes: [
        [sequelize.fn('EXTRACT', sequelize.literal('MONTH FROM date_arrivee')), 'mois'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'reservations'],
        [sequelize.fn('SUM', sequelize.col('montant_total')), 'revenus']
      ],
      group: [sequelize.fn('EXTRACT', sequelize.literal('MONTH FROM date_arrivee'))],
      order: [[sequelize.fn('EXTRACT', sequelize.literal('MONTH FROM date_arrivee')), 'ASC']],
      raw: true
    });

    res.json({
      success: true,
      data: {
        logement,
        stats: reservationStats[0] || {
          total_reservations: 0,
          total_revenus: 0,
          revenu_moyen: 0,
          total_nuits: 0
        },
        monthlyStats
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
