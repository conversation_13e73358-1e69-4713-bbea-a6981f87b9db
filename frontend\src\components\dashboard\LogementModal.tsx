import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { 
  useCreateLogementMutation, 
  useUpdateLogementMutation,
  Logement,
  CreateLogementData 
} from '@/features/api/logementsApi';

interface LogementModalProps {
  isOpen: boolean;
  onClose: () => void;
  logement: Logement | null;
  mode: 'create' | 'edit';
}

export const LogementModal: React.FC<LogementModalProps> = ({
  isOpen,
  onClose,
  logement,
  mode
}) => {
  const [formData, setFormData] = useState<CreateLogementData>({
    nom_logement: '',
    plateforme: '',
    adresse: '',
    ville: '',
    capacite: undefined,
    description: '',
    statut: 'Actif'
  });

  const { toast } = useToast();
  const [createLogement, { isLoading: isCreating }] = useCreateLogementMutation();
  const [updateLogement, { isLoading: isUpdating }] = useUpdateLogementMutation();

  useEffect(() => {
    if (mode === 'edit' && logement) {
      setFormData({
        nom_logement: logement.nom_logement,
        plateforme: logement.plateforme,
        adresse: logement.adresse || '',
        ville: logement.ville || '',
        capacite: logement.capacite,
        description: logement.description || '',
        statut: logement.statut || 'Actif'
      });
    } else {
      setFormData({
        nom_logement: '',
        plateforme: '',
        adresse: '',
        ville: '',
        capacite: undefined,
        description: '',
        statut: 'Actif'
      });
    }
  }, [mode, logement, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (mode === 'create') {
        await createLogement(formData).unwrap();
        toast({
          title: "Succès",
          description: "Logement créé avec succès",
        });
      } else if (mode === 'edit' && logement) {
        await updateLogement({ id: logement.id, ...formData }).unwrap();
        toast({
          title: "Succès",
          description: "Logement mis à jour avec succès",
        });
      }
      onClose();
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.data?.message || "Une erreur est survenue",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: keyof CreateLogementData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Créer un nouveau logement' : 'Modifier le logement'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Remplissez les informations pour créer un nouveau logement.'
              : 'Modifiez les informations du logement.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="nom_logement">Nom du logement *</Label>
              <Input
                id="nom_logement"
                value={formData.nom_logement}
                onChange={(e) => handleInputChange('nom_logement', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="plateforme">Plateforme *</Label>
              <Select 
                value={formData.plateforme} 
                onValueChange={(value) => handleInputChange('plateforme', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une plateforme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="airbnb">Airbnb</SelectItem>
                  <SelectItem value="booking">Booking.com</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="adresse">Adresse</Label>
            <Input
              id="adresse"
              value={formData.adresse}
              onChange={(e) => handleInputChange('adresse', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ville">Ville</Label>
              <Input
                id="ville"
                value={formData.ville}
                onChange={(e) => handleInputChange('ville', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacite">Capacité (personnes)</Label>
              <Input
                id="capacite"
                type="number"
                min="1"
                value={formData.capacite || ''}
                onChange={(e) => handleInputChange('capacite', e.target.value ? parseInt(e.target.value) : undefined)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="statut">Statut</Label>
            <Select 
              value={formData.statut} 
              onValueChange={(value) => handleInputChange('statut', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Actif">Actif</SelectItem>
                <SelectItem value="Inactif">Inactif</SelectItem>
                <SelectItem value="Maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Annuler
            </Button>
            <Button 
              type="submit" 
              disabled={isCreating || isUpdating || !formData.nom_logement || !formData.plateforme}
            >
              {isCreating || isUpdating ? 'Enregistrement...' : 
               mode === 'create' ? 'Créer' : 'Mettre à jour'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
