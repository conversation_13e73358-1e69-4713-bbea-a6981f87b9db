import React, { useState, useEffect } from 'react';
import { Building2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  useCreateLogementMutation,
  useUpdateLogementMutation,
  Logement,
  CreateLogementData
} from '@/features/api/logementsApi';

interface LogementModalProps {
  isOpen: boolean;
  onClose: () => void;
  logement: Logement | null;
  mode: 'create' | 'edit';
}

export const LogementModal: React.FC<LogementModalProps> = ({
  isOpen,
  onClose,
  logement,
  mode
}) => {
  const [formData, setFormData] = useState<CreateLogementData>({
    nom_logement: '',
    plateforme: '',
    adresse: '',
    ville: '',
    capacite: undefined,
    description: '',
    statut: 'Actif'
  });

  const { toast } = useToast();
  const [createLogement, { isLoading: isCreating }] = useCreateLogementMutation();
  const [updateLogement, { isLoading: isUpdating }] = useUpdateLogementMutation();

  useEffect(() => {
    if (mode === 'edit' && logement) {
      setFormData({
        nom_logement: logement.nom_logement,
        plateforme: logement.plateforme,
        adresse: logement.adresse || '',
        ville: logement.ville || '',
        capacite: logement.capacite,
        description: logement.description || '',
        statut: logement.statut || 'Actif'
      });
    } else {
      setFormData({
        nom_logement: '',
        plateforme: '',
        adresse: '',
        ville: '',
        capacite: undefined,
        description: '',
        statut: 'Actif'
      });
    }
  }, [mode, logement, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation côté client
    if (!formData.nom_logement.trim()) {
      toast({
        title: "Erreur",
        description: "Le nom du logement est requis",
        variant: "destructive",
      });
      return;
    }

    if (!formData.plateforme) {
      toast({
        title: "Erreur",
        description: "La plateforme est requise",
        variant: "destructive",
      });
      return;
    }

    try {
      if (mode === 'create') {
        const result = await createLogement(formData).unwrap();
        toast({
          title: "Succès",
          description: "Logement créé avec succès",
        });
        console.log('Logement créé:', result);
      } else if (mode === 'edit' && logement) {
        const updateData = { id: logement.id, ...formData };
        console.log('Données de mise à jour:', updateData);

        const result = await updateLogement(updateData).unwrap();
        toast({
          title: "Succès",
          description: "Logement mis à jour avec succès",
        });
        console.log('Logement mis à jour:', result);
      }
      onClose();
    } catch (error: any) {
      console.error('Erreur lors de la soumission:', error);
      toast({
        title: "Erreur",
        description: error.data?.message || error.message || "Une erreur est survenue",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: keyof CreateLogementData, value: string | number | undefined) => {
    console.log(`Changement de ${field}:`, value);
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] rounded-2xl border-slate-200 dark:border-slate-700 shadow-2xl">
        <DialogHeader className="space-y-4 pb-6">
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent flex items-center gap-3">
            <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl shadow-lg">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            {mode === 'create' ? 'Créer un nouveau logement' : 'Modifier le logement'}
          </DialogTitle>
          <DialogDescription className="text-slate-600 dark:text-slate-400 text-lg">
            {mode === 'create'
              ? 'Remplissez les informations pour créer un nouveau logement sur votre plateforme.'
              : 'Modifiez les informations du logement sélectionné.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="nom_logement">Nom du logement *</Label>
              <Input
                id="nom_logement"
                value={formData.nom_logement}
                onChange={(e) => handleInputChange('nom_logement', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="plateforme">Plateforme *</Label>
              <Select 
                value={formData.plateforme} 
                onValueChange={(value) => handleInputChange('plateforme', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une plateforme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="airbnb">Airbnb</SelectItem>
                  <SelectItem value="booking">Booking.com</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="adresse">Adresse</Label>
            <Input
              id="adresse"
              value={formData.adresse}
              onChange={(e) => handleInputChange('adresse', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ville">Ville</Label>
              <Input
                id="ville"
                value={formData.ville}
                onChange={(e) => handleInputChange('ville', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacite">Capacité (personnes)</Label>
              <Input
                id="capacite"
                type="number"
                min="1"
                value={formData.capacite || ''}
                onChange={(e) => handleInputChange('capacite', e.target.value ? parseInt(e.target.value) : undefined)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="statut">Statut</Label>
            <Select 
              value={formData.statut} 
              onValueChange={(value) => handleInputChange('statut', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Actif">Actif</SelectItem>
                <SelectItem value="Inactif">Inactif</SelectItem>
                <SelectItem value="Maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
            />
          </div>

          <DialogFooter className="gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="px-6 py-2 border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 rounded-xl font-medium transition-all duration-200"
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={isCreating || isUpdating || !formData.nom_logement || !formData.plateforme}
              className="px-8 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              {isCreating || isUpdating ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Enregistrement...
                </div>
              ) : (
                mode === 'create' ? '✨ Créer le logement' : '💾 Mettre à jour'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
