const express = require('express');
const router = express.Router();
const reservationsController = require('../controllers/reservations.controller');
const { verifyToken, isAdmin } = require('../middlewares/auth.middleware');

// Routes CRUD pour les réservations
router.get('/', verifyToken, reservationsController.getAllReservations);
router.get('/:id', verifyToken, reservationsController.getReservationById);
router.post('/', verifyToken, reservationsController.createReservation);
router.put('/:id', verifyToken, reservationsController.updateReservation);
router.delete('/:id', verifyToken, reservationsController.deleteReservation);

// Route pour supprimer toutes les réservations (admin seulement)
router.delete('/all', verifyToken, isAdmin, reservationsController.deleteAllReservations);

// Route de test
router.get('/test', (req, res) => {
  res.json({ message: 'API de réservations fonctionne correctement' });
});

module.exports = router;




