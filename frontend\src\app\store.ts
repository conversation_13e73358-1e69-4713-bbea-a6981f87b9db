import { configureStore } from '@reduxjs/toolkit';
import { authApi } from '../features/api/auth';
import { reservationsApi } from '@/features/api/reservationsApi';
import { importApi } from '@/features/api/importApi';
import { predictionApi } from '@/features/api/predictionApi';
import { logementsApi } from '@/features/api/logementsApi';

export const store = configureStore({
  reducer: {
    [authApi.reducerPath]: authApi.reducer,
    [reservationsApi.reducerPath]: reservationsApi.reducer,
    [importApi.reducerPath]: importApi.reducer,
    [predictionApi.reducerPath]: predictionApi.reducer,
    [logementsApi.reducerPath]: logementsApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      reservationsApi.middleware,
      importApi.middleware,
      predictionApi.middleware,
      logementsApi.middleware
    ),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
