import React, { useEffect, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  Upload,
  Calendar,
  Building2,
  Brain,
  Users,
  Trash2,
  RefreshCw,
  X
} from 'lucide-react';

interface NotificationToastProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  category: 'import' | 'reservation' | 'logement' | 'ia' | 'user' | 'system';
  duration?: number;
  onClose?: () => void;
}

export const NotificationToast: React.FC<NotificationToastProps> = ({
  type,
  title,
  message,
  category,
  duration = 5000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onClose?.(), 300); // Délai pour l'animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getIcon = () => {
    if (type === 'success') return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (type === 'error') return <AlertCircle className="w-5 h-5 text-red-500" />;
    if (type === 'warning') return <AlertCircle className="w-5 h-5 text-yellow-500" />;
    
    // Icônes par catégorie
    switch (category) {
      case 'import': return <Upload className="w-5 h-5 text-blue-500" />;
      case 'reservation': return <Calendar className="w-5 h-5 text-indigo-500" />;
      case 'logement': return <Building2 className="w-5 h-5 text-emerald-500" />;
      case 'ia': return <Brain className="w-5 h-5 text-pink-500" />;
      case 'user': return <Users className="w-5 h-5 text-orange-500" />;
      default: return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';
      case 'error': return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      default: return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
    }
  };

  const getCategoryEmoji = () => {
    switch (category) {
      case 'import': return '📥';
      case 'reservation': return '📋';
      case 'logement': return '🏠';
      case 'ia': return '🤖';
      case 'user': return '👥';
      default: return '🔔';
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`
      fixed top-4 right-4 z-50 max-w-md w-full
      transform transition-all duration-300 ease-in-out
      ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
    `}>
      <div className={`
        border rounded-xl shadow-lg p-4
        ${getBackgroundColor()}
        backdrop-blur-sm
      `}>
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-0.5">
            {getIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-lg">{getCategoryEmoji()}</span>
              <h4 className="font-semibold text-sm text-slate-800 dark:text-slate-200">
                {title}
              </h4>
            </div>
            
            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
              {message}
            </p>
            
            <div className="flex items-center justify-between mt-3">
              <span className="text-xs text-slate-500 dark:text-slate-500">
                {new Date().toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
              
              <button
                onClick={() => {
                  setIsVisible(false);
                  setTimeout(() => onClose?.(), 300);
                }}
                className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook pour utiliser les notifications toast améliorées
export const useNotificationToast = () => {
  const { toast } = useToast();

  const showNotification = (notification: Omit<NotificationToastProps, 'onClose'>) => {
    // Utiliser le système de toast existant avec des améliorations
    toast({
      title: `${getCategoryEmoji(notification.category)} ${notification.title}`,
      description: notification.message,
      variant: notification.type === 'error' ? 'destructive' : 'default',
    });
  };

  const showImportNotification = (type: 'start' | 'success' | 'error', details?: any) => {
    switch (type) {
      case 'start':
        showNotification({
          type: 'info',
          title: 'Import en cours',
          message: 'Traitement du fichier en cours...',
          category: 'import'
        });
        break;
      case 'success':
        showNotification({
          type: 'success',
          title: 'Import réussi',
          message: `${details?.count || 0} éléments importés avec succès`,
          category: 'import'
        });
        break;
      case 'error':
        showNotification({
          type: 'error',
          title: 'Erreur d\'import',
          message: details?.message || 'Une erreur est survenue lors de l\'import',
          category: 'import'
        });
        break;
    }
  };

  const showReservationNotification = (action: 'create' | 'update' | 'delete', details?: any) => {
    const actions = {
      create: { title: 'Réservation créée', message: 'Nouvelle réservation ajoutée avec succès' },
      update: { title: 'Réservation modifiée', message: 'Réservation mise à jour avec succès' },
      delete: { title: 'Réservation supprimée', message: 'Réservation supprimée avec succès' }
    };

    showNotification({
      type: 'success',
      title: actions[action].title,
      message: details?.message || actions[action].message,
      category: 'reservation'
    });
  };

  const showLogementNotification = (action: 'create' | 'update' | 'delete', details?: any) => {
    const actions = {
      create: { title: 'Logement créé', message: 'Nouveau logement ajouté avec succès' },
      update: { title: 'Logement modifié', message: 'Logement mis à jour avec succès' },
      delete: { title: 'Logement supprimé', message: 'Logement supprimé avec succès' }
    };

    showNotification({
      type: 'success',
      title: actions[action].title,
      message: details?.message || actions[action].message,
      category: 'logement'
    });
  };

  const showIANotification = (type: 'analysis' | 'prediction' | 'update', details?: any) => {
    const types = {
      analysis: { title: 'Analyse IA terminée', message: 'Nouvelle analyse générée avec succès' },
      prediction: { title: 'Prédictions mises à jour', message: 'Nouvelles prédictions disponibles' },
      update: { title: 'IA mise à jour', message: 'Les données IA ont été actualisées' }
    };

    showNotification({
      type: 'info',
      title: types[type].title,
      message: details?.message || types[type].message,
      category: 'ia'
    });
  };

  const showSystemNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    showNotification({
      type,
      title: 'Notification système',
      message,
      category: 'system'
    });
  };

  return {
    showNotification,
    showImportNotification,
    showReservationNotification,
    showLogementNotification,
    showIANotification,
    showSystemNotification
  };
};

// Fonction utilitaire pour les émojis
const getCategoryEmoji = (category: string) => {
  switch (category) {
    case 'import': return '📥';
    case 'reservation': return '📋';
    case 'logement': return '🏠';
    case 'ia': return '🤖';
    case 'user': return '👥';
    default: return '🔔';
  }
};
