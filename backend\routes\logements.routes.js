const express = require('express');
const router = express.Router();
const logementsController = require('../controllers/logements.controller');
const photoUpload = require('../middlewares/photo-upload.middleware');
const { verifyToken, isAdmin } = require('../middlewares/auth.middleware');

// Routes publiques (lecture seule)
// Récupérer tous les logements
router.get('/', logementsController.getAllLogements);

// Récupérer un logement par ID
router.get('/:id', logementsController.getLogementById);

// Récupérer les statistiques d'un logement
router.get('/:id/stats', logementsController.getLogementStats);

// Routes protégées (admin seulement)
// Créer un nouveau logement
router.post('/', verifyToken, isAdmin, logementsController.createLogement);

// Mettre à jour un logement
router.put('/:id', verifyToken, isAdmin, logementsController.updateLogement);

// Supprimer un logement
router.delete('/:id', verifyToken, isAdmin, logementsController.deleteLogement);

// Ajouter des photos à un logement
router.post('/:id/photos', 
  verifyToken, 
  isAdmin, 
  photoUpload.array('photos', 10), 
  logementsController.addPhotos
);

// Supprimer une photo d'un logement
router.delete('/:id/photos/:photoIndex', 
  verifyToken, 
  isAdmin, 
  logementsController.deletePhoto
);

module.exports = router;
