const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Logement = sequelize.define('Logement', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nom_logement: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    plateforme: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: { isIn: [['airbnb', 'booking']] }
    },
    adresse: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    ville: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    capacite: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    statut: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: 'Actif',
      validate: { isIn: [['Actif', 'Inactif', 'Maintenance']] }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    photos: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'logements',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  Logement.associate = function(models) {
    Logement.hasMany(models.Reservation, {
      foreignKey: 'logement_id',
      as: 'reservations'
    });
  };

  return Logement;
};