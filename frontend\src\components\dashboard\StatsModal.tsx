import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, TrendingUp, DollarSign, Moon } from 'lucide-react';
import { 
  useGetLogementStatsQuery,
  Logement 
} from '@/features/api/logementsApi';

interface StatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  logement: Logement | null;
}

export const StatsModal: React.FC<StatsModalProps> = ({
  isOpen,
  onClose,
  logement
}) => {
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

  const { 
    data: statsResponse, 
    isLoading, 
    error 
  } = useGetLogementStatsQuery(
    { id: logement?.id || 0, year: selectedYear },
    { skip: !logement }
  );

  const stats = statsResponse?.data;

  const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(num || 0);
  };

  const getMonthName = (monthNumber: number) => {
    const months = [
      'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
      'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
    ];
    return months[monthNumber - 1] || '';
  };

  if (!logement) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Statistiques - {logement.nom_logement}</DialogTitle>
          <DialogDescription>
            Consultez les performances de ce logement
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Sélecteur d'année */}
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Année :</label>
            <Select 
              value={selectedYear.toString()} 
              onValueChange={(value) => setSelectedYear(parseInt(value))}
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 5 }, (_, i) => {
                  const year = new Date().getFullYear() - i;
                  return (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              Erreur lors du chargement des statistiques
            </div>
          ) : stats ? (
            <>
              {/* Statistiques générales */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <CalendarDays className="w-4 h-4" />
                      Réservations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.stats.total_reservations || 0}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <DollarSign className="w-4 h-4" />
                      Revenus totaux
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(stats.stats.total_revenus || 0)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      Revenu moyen
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(stats.stats.revenu_moyen || 0)}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Moon className="w-4 h-4" />
                      Nuits totales
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.stats.total_nuits || 0}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Informations du logement */}
              <Card>
                <CardHeader>
                  <CardTitle>Informations du logement</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Plateforme:</span>
                    <Badge className={
                      logement.plateforme === 'airbnb' 
                        ? 'bg-[#FF5A5F] text-white' 
                        : 'bg-[#003580] text-white'
                    }>
                      {logement.plateforme === 'airbnb' ? 'Airbnb' : 'Booking.com'}
                    </Badge>
                  </div>
                  {logement.ville && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Ville:</span>
                      <span>{logement.ville}</span>
                    </div>
                  )}
                  {logement.capacite && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Capacité:</span>
                      <span>{logement.capacite} personnes</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Statut:</span>
                    <Badge variant={logement.statut === 'Actif' ? 'default' : 'secondary'}>
                      {logement.statut || 'Actif'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Statistiques mensuelles */}
              {stats.monthlyStats && stats.monthlyStats.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Répartition mensuelle {selectedYear}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {stats.monthlyStats.map((monthStat) => (
                        <div key={monthStat.mois} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="font-medium">
                            {getMonthName(monthStat.mois)}
                          </div>
                          <div className="flex items-center gap-4 text-sm">
                            <span>{monthStat.reservations} réservations</span>
                            <span className="font-medium">
                              {formatCurrency(monthStat.revenus || 0)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              Aucune donnée disponible pour cette période
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
