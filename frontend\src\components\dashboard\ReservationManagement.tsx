import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { 
  useGetAllReservationsQuery, 
  useDeleteReservationMutation 
} from '@/features/api/reservationsApi';
import { 
  Calendar, 
  Users, 
  DollarSign, 
  Trash2, 
  Search,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

export const ReservationManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  const {
    data: reservationsResponse,
    isLoading: loading,
    error,
    refetch
  } = useGetAllReservationsQuery();

  const [deleteReservation] = useDeleteReservationMutation();

  const reservations = reservationsResponse?.data || [];

  // Filtrer les réservations selon la recherche
  const filteredReservations = reservations.filter(reservation =>
    reservation.nom_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reservation.email_client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reservation.logement?.nom_logement?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteReservation = async (reservation: any) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la réservation de ${reservation.nom_client} ?`)) {
      try {
        await deleteReservation(reservation.id).unwrap();
        toast({
          title: "✅ Réservation supprimée",
          description: "La réservation a été supprimée avec succès. Les analyses IA ont été mises à jour.",
        });
      } catch (error: any) {
        toast({
          title: "❌ Erreur",
          description: error.data?.message || "Erreur lors de la suppression",
          variant: "destructive",
        });
      }
    }
  };

  const getStatusColor = (statut: string) => {
    switch (statut?.toLowerCase()) {
      case 'confirmee':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'en_attente':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'annulee':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (statut: string) => {
    switch (statut?.toLowerCase()) {
      case 'confirmee':
        return '✅';
      case 'en_attente':
        return '⏳';
      case 'annulee':
        return '❌';
      default:
        return '❓';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-3">
            <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-2 rounded-xl shadow-lg">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            📋 Gestion des Réservations
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            {filteredReservations.length} réservation(s) • Suppression met à jour les analyses IA
          </p>
        </div>

        <div className="flex gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
            <Input
              type="search"
              placeholder="Rechercher une réservation..."
              className="pl-10 w-[280px] h-12 border-slate-200 dark:border-slate-700 rounded-xl"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Button
            onClick={() => refetch()}
            variant="outline"
            className="h-12 px-4 border-slate-200 dark:border-slate-700 rounded-xl"
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Alerte sur l'impact des suppressions */}
      <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
        <div className="flex items-center gap-3">
          <AlertTriangle className="w-5 h-5 text-amber-600" />
          <div>
            <h4 className="font-semibold text-amber-800">Impact sur les analyses IA</h4>
            <p className="text-sm text-amber-700">
              La suppression d'une réservation met automatiquement à jour toutes les courbes et prédictions IA en temps réel.
            </p>
          </div>
        </div>
      </div>

      {/* Liste des réservations */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center gap-3">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-slate-600 dark:text-slate-400">Chargement des réservations...</p>
          </div>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl">
            <p className="font-medium">❌ Erreur lors du chargement des réservations</p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredReservations.length > 0 ? (
            filteredReservations.map((reservation) => (
              <Card key={reservation.id} className="hover:shadow-lg transition-all duration-300 border-slate-200 dark:border-slate-700 rounded-2xl overflow-hidden">
                <CardHeader className="pb-3 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg font-bold text-slate-800 dark:text-slate-100">
                        {reservation.nom_client}
                      </CardTitle>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        {reservation.logement?.nom_logement || 'Logement non spécifié'}
                      </p>
                    </div>
                    <Badge className={`${getStatusColor(reservation.statut_reservation)} border`}>
                      {getStatusIcon(reservation.statut_reservation)} {reservation.statut_reservation || 'Inconnu'}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4 p-6">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-blue-500" />
                      <div>
                        <p className="font-medium">Arrivée</p>
                        <p className="text-slate-600 dark:text-slate-400">
                          {new Date(reservation.date_arrivee).toLocaleDateString('fr-FR')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-red-500" />
                      <div>
                        <p className="font-medium">Départ</p>
                        <p className="text-slate-600 dark:text-slate-400">
                          {new Date(reservation.date_depart).toLocaleDateString('fr-FR')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-green-500" />
                      <div>
                        <p className="font-medium">Personnes</p>
                        <p className="text-slate-600 dark:text-slate-400">{reservation.nb_personnes}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-purple-500" />
                      <div>
                        <p className="font-medium">Montant</p>
                        <p className="text-slate-600 dark:text-slate-400 font-bold">
                          {parseFloat(reservation.montant_total).toLocaleString('fr-FR')}€
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteReservation(reservation)}
                      className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-md"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Supprimer la réservation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-2xl p-8">
                <Calendar className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-slate-600 dark:text-slate-400 mb-2">
                  Aucune réservation trouvée
                </h3>
                <p className="text-slate-500 dark:text-slate-500">
                  {searchTerm ? 'Essayez de modifier votre recherche' : 'Importez des données pour voir les réservations'}
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
