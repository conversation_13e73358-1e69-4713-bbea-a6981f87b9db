import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export interface AnalyticsData {
  occupancyRate: {
    monthly: Array<{ month: string; rate: number; logement: string }>;
    yearly: Array<{ year: number; rate: number }>;
  };
  revenue: {
    monthly: Array<{ month: string; revenue: number; logement: string; plateforme: string }>;
    yearly: Array<{ year: number; revenue: number }>;
    comparison: Array<{ logement: string; revenue: number; occupancy: number; plateforme: string }>;
  };
  predictions: {
    pricing: Array<{ period: string; suggestedPrice: number; currentPrice: number; season: string }>;
    weakPeriods: Array<{ period: string; level: 'low' | 'medium' | 'high'; suggestions: string[] }>;
    trends: Array<{ month: string; predicted: number; confidence: number }>;
  };
  insights: {
    bestPerformers: Array<{ logement: string; metric: string; value: number }>;
    improvements: Array<{ category: string; suggestion: string; impact: string }>;
  };
}

export interface AnalyticsFilters {
  period?: string;
  logement_id?: string;
}

export const analyticsApi = createApi({
  reducerPath: 'analyticsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:3000/api/analytics',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token ?? localStorage.getItem('authToken');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    }
  }),
  tagTypes: ['Analytics', 'Reservation', 'Logement'],
  endpoints: (builder) => ({
    // Récupérer l'analyse des performances
    getPerformanceAnalytics: builder.query<{ success: boolean; data: any }, AnalyticsFilters | void>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        if (filters.period) params.append('period', filters.period);
        if (filters.logement_id) params.append('logement_id', filters.logement_id);

        return `/performance?${params.toString()}`;
      },
      providesTags: ['Analytics', 'Reservation', 'Logement']
    }),

    // Récupérer les prédictions IA
    getAIPredictions: builder.query<{ success: boolean; data: any }, void>({
      query: () => '/predictions',
      providesTags: ['Analytics', 'Reservation', 'Logement']
    }),

    // Récupérer les insights IA
    getAIInsights: builder.query<{ success: boolean; data: any }, void>({
      query: () => '/insights',
      providesTags: ['Analytics', 'Reservation', 'Logement']
    }),

    // Récupérer l'analyse complète IA
    getCompleteAnalysis: builder.query<{ success: boolean; data: AnalyticsData; message: string }, AnalyticsFilters | void>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        if (filters.period) params.append('period', filters.period);
        if (filters.logement_id) params.append('logement_id', filters.logement_id);

        return `/complete?${params.toString()}`;
      },
      providesTags: ['Analytics', 'Reservation', 'Logement']
    })
  })
});

export const {
  useGetPerformanceAnalyticsQuery,
  useGetAIPredictionsQuery,
  useGetAIInsightsQuery,
  useGetCompleteAnalysisQuery
} = analyticsApi;
