import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { Reservation } from '@/types/Reservation';

export const reservationsApi = createApi({
  reducerPath: 'reservationsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:3000/api', // Match backend URL
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token ?? localStorage.getItem('authToken');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    }
  }),
  tagTypes: ['Reservation', 'Logement', 'Analytics'],
  endpoints: (builder) => ({
    importReservations: builder.mutation<
      {
        success: boolean;
        message: string;
        results: any;
        importedReservations: Reservation[];
      },
      FormData
    >({
      query: (formData) => ({
        url: '/import',
        method: 'POST',
        body: formData
      }),
      invalidatesTags: ['Reservation', 'Logement', 'Analytics']
    }),
    getAllReservations: builder.query<{ success: boolean; data: Reservation[] }, void>({
      query: () => '/reservations',
      providesTags: ['Reservation']
    }),
    getReservationById: builder.query<{ success: boolean; data: Reservation }, number>({
      query: (id) => `/reservations/${id}`,
      providesTags: (result, error, id) => [{ type: 'Reservation', id }]
    }),
    createReservation: builder.mutation<{ success: boolean; data: Reservation }, Partial<Reservation>>({
      query: (reservation) => ({
        url: '/reservations',
        method: 'POST',
        body: reservation
      }),
      invalidatesTags: ['Reservation', 'Logement', 'Analytics']
    }),
    updateReservation: builder.mutation<{ success: boolean; data: Reservation }, { id: number; reservation: Partial<Reservation> }>({
      query: ({ id, reservation }) => ({
        url: `/reservations/${id}`,
        method: 'PUT',
        body: reservation
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Reservation', id },
        'Reservation',
        'Logement',
        'Analytics'
      ]
    }),
    deleteReservation: builder.mutation<{ success: boolean; message: string }, number>({
      query: (id) => ({
        url: `/reservations/${id}`,
        method: 'DELETE'
      }),
      invalidatesTags: ['Reservation', 'Logement', 'Analytics']
    }),
  })
});

// ✅ Hooks
export const {
  useImportReservationsMutation,
  useGetAllReservationsQuery,
  useGetReservationByIdQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useDeleteReservationMutation
} = reservationsApi;
