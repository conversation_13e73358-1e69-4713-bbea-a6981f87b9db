import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { 
  Trash2, 
  Building2, 
  Filter, 
  Loader2 
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { FileUpload } from '@/components/FileUpload';
import { useImportReservationsMutation } from '@/features/api/reservationsApi';

// Add this component at the top level of your file
const DeleteReservationsButton = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const handleDelete = async () => {
    console.log("Delete button clicked");
    
    try {
      toast({
        title: "Suppression en cours",
        description: "Veuillez patienter...",
      });
      
      const response = await fetch('http://localhost:3000/api/reservations/all', {
        method: 'DELETE',
      });
      
      console.log("Delete API response:", response.status);
      
      if (response.ok) {
        toast({
          title: "Succès",
          description: "Toutes les réservations ont été supprimées",
        });
        
        // Force page reload to reflect changes
        window.location.reload();
      } else {
        throw new Error("Échec de la suppression");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast({
        title: "Erreur",
        description: "Impossible de supprimer les réservations",
        variant: "destructive",
      });
    }
  };
  
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" className="flex items-center gap-2">
          <Trash2 className="h-4 w-4" />
          Supprimer
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
          <AlertDialogDescription>
            Cette action supprimera définitivement toutes les réservations. Cette action ne peut pas être annulée.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Annuler</AlertDialogCancel>
          <Button variant="destructive" onClick={handleDelete}>
            Supprimer
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

interface Reservation {
  plateforme: string;
  id: string | number;
  logement?: {
    nom_logement?: string;
  };
  nom_client?: string;
  date_arrivee?: string;
  date_depart?: string;
  statut?: string;
  montant_total?: number;
  source_plateforme?: string;
}

export const ImportTab = () => {
  const { toast } = useToast();
  const navigate = useNavigate();

  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [filteredReservations, setFilteredReservations] = useState<Reservation[]>([]);
  const [loadingReservations, setLoadingReservations] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(20);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [platformFilter, setPlatformFilter] = useState<string>("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const [importReservations, { isLoading: isImporting }] = useImportReservationsMutation();

  const fetchReservations = useCallback(async (pageToFetch = 1) => {
    setLoadingReservations(true);
    setFetchError(null);

    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      const res = await fetch(
        `http://localhost:3000/api/reservations?page=${pageToFetch}&limit=${limit}`, 
        { signal: controller.signal }
      );
      
      clearTimeout(timeoutId);
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      const data = await res.json();

      if (data.success) {
        setReservations(data.data);
        setPage(data.pagination.page);
        setTotalPages(data.pagination.pages);
      } else {
        setFetchError(data.message || "Erreur lors du chargement des réservations");
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          setFetchError("La requête a pris trop de temps. Veuillez réessayer.");
        } else {
          setFetchError("Erreur serveur: " + err.message);
        }
      } else {
        setFetchError("Erreur serveur inconnue");
      }
    } finally {
      setLoadingReservations(false);
    }
  }, [limit]); // Only depend on limit to prevent unnecessary re-renders

  // Apply filter when platformFilter or reservations change
  useEffect(() => {
    if (platformFilter === "all") {
      setFilteredReservations(reservations);
    } else {
      setFilteredReservations(
        reservations.filter(
          (resv) => resv.source_plateforme?.toLowerCase() === platformFilter.toLowerCase()
        )
      );
    }
  }, [platformFilter, reservations]);

  useEffect(() => {
    fetchReservations(page);
  }, [page, fetchReservations]);

  const handleFileSelect = async (file: File) => {
    setUploadError(null);

    // Notification de début d'importation
    toast({
      title: "🚀 Début de l'importation",
      description: `Traitement du fichier "${file.name}" en cours...`,
    });

    // Validate file type
    const validExtensions = ['.csv', '.xls', '.xlsx'];
    const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    if (!validExtensions.includes(fileExt)) {
      const errorMsg = "Format de fichier non supporté. Veuillez télécharger un fichier CSV ou Excel (.csv, .xls, .xlsx).";
      setUploadError(errorMsg);
      toast({
        title: "❌ Format invalide",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      const errorMsg = "La taille du fichier ne doit pas dépasser 10MB.";
      setUploadError(errorMsg);
      toast({
        title: "❌ Fichier trop volumineux",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    // Notification de validation réussie
    toast({
      title: "✅ Fichier validé",
      description: `Fichier "${file.name}" (${(file.size / 1024 / 1024).toFixed(2)} MB) prêt pour l'importation`,
    });

    const formData = new FormData();
    formData.append("file", file);

    try {
      const result = await importReservations(formData).unwrap();

      // Notification de succès détaillée
      const insertedCount = result.results?.inserted || 0;
      const updatedCount = result.results?.updated || 0;
      const totalCount = insertedCount + updatedCount;

      toast({
        title: "🎉 Import terminé avec succès !",
        description: `📁 Fichier: ${file.name}
📊 ${totalCount} réservations traitées
➕ ${insertedCount} nouvelles réservations
🔄 ${updatedCount} réservations mises à jour
🏠 Logements créés automatiquement`,
      });

      // Notification supplémentaire pour les sections mises à jour
      setTimeout(() => {
        toast({
          title: "🔄 Synchronisation terminée",
          description: "Les sections Logements, Réservations et IA ont été mises à jour automatiquement",
        });
      }, 2000);

      setPage(1);
      fetchReservations(1);
    } catch (error: unknown) {
      // Type guard to safely access error properties
      if (error && typeof error === 'object' && 'data' in error) {
        const apiError = error as { data?: { message?: string } };
        const errorMsg = apiError.data?.message || "Une erreur est survenue lors de l'importation.";
        setUploadError(errorMsg);

        toast({
          title: "❌ Erreur d'importation",
          description: `📁 Fichier: ${file.name}
⚠️ ${errorMsg}
💡 Vérifiez le format de votre fichier`,
          variant: "destructive",
        });
      } else {
        // Fallback for other error types
        const errorMsg = "Une erreur inattendue est survenue";
        setUploadError(errorMsg);

        toast({
          title: "❌ Erreur inattendue",
          description: `📁 Fichier: ${file.name}
⚠️ ${errorMsg}
🔄 Veuillez réessayer`,
          variant: "destructive",
        });
      }
    }
  };

  // Fonction simplifiée pour supprimer toutes les réservations
  const handleDeleteAllReservations = async () => {
    try {
      console.log("Tentative de suppression de toutes les réservations...");

      // Notification de début de suppression
      toast({
        title: "🗑️ Suppression en cours",
        description: "Suppression de toutes les réservations en cours...",
      });

      const response = await fetch('http://localhost:3000/api/reservations/all', {
        method: 'DELETE'
      });

      console.log("Réponse:", response.status);

      if (response.ok) {
        // Notification de succès détaillée
        toast({
          title: "🎉 Suppression terminée",
          description: `✅ Toutes les réservations ont été supprimées
🔄 Les analyses IA vont se mettre à jour
📊 Les statistiques seront recalculées`,
        });

        // Notification supplémentaire pour les sections affectées
        setTimeout(() => {
          toast({
            title: "🔄 Mise à jour automatique",
            description: "Les sections Logements, Réservations et IA reflètent maintenant les changements",
          });
        }, 2000);

        window.location.reload(); // Recharger la page
      } else {
        // Essayer de lire le texte de la réponse au lieu du JSON
        const errorText = await response.text();
        console.error("Erreur texte:", errorText);
        throw new Error("Erreur lors de la suppression (statut " + response.status + ")");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast({
        title: "❌ Erreur de suppression",
        description: `⚠️ Impossible de supprimer les réservations
🔍 Détails: ${error instanceof Error ? error.message : "Erreur inconnue"}
🔄 Veuillez réessayer`,
        variant: "destructive",
      });
    }
  };

  // Suppression de la fonction testDeleteAPI inutile
  // function testDeleteAPI(event: React.MouseEvent<HTMLButtonElement>): void {
  //   throw new Error('Function not implemented.');
  // }

  // Suppression du code commenté pour testDeleteAPI

  // Créons un nouveau composant de bouton de suppression qui fonctionne correctement
  const TruncateReservationsButton = () => {
    const { toast } = useToast();
    
    const handleTruncate = async () => {
      try {
        // Afficher un toast de chargement
        toast({
          title: "Suppression en cours",
          description: "Vidage de la table des réservations...",
        });
        
        // Appel à l'API pour effectuer un TRUNCATE de la table
        const response = await fetch('http://localhost:3000/api/reservations/all', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-Truncate-Table': 'true' // En-tête spécial pour indiquer un truncate
          }
        });
        
        if (!response.ok) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }
        
        // Succès
        toast({
          title: "Table vidée",
          description: "Toutes les réservations ont été supprimées de la base de données",
        });
        
        // Recharger la page pour actualiser l'affichage
        window.location.reload();
      } catch (error) {
        console.error("Erreur lors du truncate:", error);
        toast({
          title: "Erreur",
          description: error instanceof Error ? error.message : "Erreur lors de la suppression",
          variant: "destructive",
        });
      }
    };
    
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="destructive" className="flex items-center gap-2">
            <Trash2 className="h-4 w-4" />
            Vider la table
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Vider la table des réservations ?</AlertDialogTitle>
            <AlertDialogDescription>
              Cette action va supprimer TOUTES les réservations de la base de données (TRUNCATE). 
              Cette opération est irréversible.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annuler</AlertDialogCancel>
            <Button variant="destructive" onClick={handleTruncate}>
              Vider la table
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <div className="space-y-8 animate-fade-in max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Upload Section */}
      <Card className="lg:col-span-2 shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-lg font-semibold text-rent-blue-700">
            Importer des fichiers de réservations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <FileUpload 
            onFileSelect={handleFileSelect}
            isLoading={isImporting}
            error={uploadError}
          />
        </CardContent>
      </Card>

      {/* Reservations Table with Filter */}
      <Card className="shadow-md">
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <CardTitle className="flex items-center gap-3 text-lg font-semibold text-rent-blue-700">
              <Building2 className="w-6 h-6" />
              Liste des réservations
            </CardTitle>
            
            <div className="flex flex-wrap gap-2">
              {/* Platform Filter */}
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <SelectValue placeholder="Filtrer par plateforme" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les plateformes</SelectItem>
                  <SelectItem value="airbnb">Airbnb</SelectItem>
                  <SelectItem value="booking">Booking</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Bouton direct sans confirmation */}
              <Button 
                variant="destructive" 
                className="flex items-center gap-2"
                onClick={handleDeleteAllReservations}
              >
                <Trash2 className="h-4 w-4" />
                Supprimer
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardHeader>
          <div className="flex justify-between items-center mt-6">
            <Button
              onClick={() => setPage((p) => Math.max(p - 1, 1))}
              disabled={page <= 1}
              variant="outline"
              aria-label="Page précédente"
            >
              Précédent
            </Button>
            <span className="text-sm text-gray-700">
              Page {page} / {totalPages}
            </span>
            <Button
              onClick={() => setPage((p) => (p < totalPages ? p + 1 : p))}
              disabled={page >= totalPages}
              variant="outline"
              aria-label="Page suivante"
            >
              Suivant
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {loadingReservations ? (
            <div className="flex justify-center items-center gap-2 text-gray-600 font-medium">
              <Loader2 className="animate-spin w-5 h-5" />
              Chargement des réservations...
            </div>
          ) : fetchError ? (
            <div className="text-red-600 text-center space-y-2">
              <p>{fetchError}</p>
              <Button variant="outline" onClick={() => fetchReservations(page)}>
                Réessayer
              </Button>
            </div>
          ) : filteredReservations.length === 0 ? (
            <p className="text-center text-gray-500">
              {reservations.length === 0 
                ? "Aucune réservation trouvée." 
                : `Aucune réservation trouvée pour la plateforme "${platformFilter}".`}
            </p>
          ) : (
            <>
              <div className="overflow-x-auto max-w-full">
                <table className="w-full border-collapse border border-gray-300 min-w-[700px]">
                  <thead className="bg-gray-100 sticky top-0">
                    <tr>
                      {[
                        "ID",
                        "Logement",
                        "Nom client",
                        "Date arrivée",
                        "Date départ",
                        "Statut",
                        "Montant total",
                        "Plateforme",
                      ].map((header) => (
                        <th
                          key={header}
                          className="border border-gray-300 p-2 text-left text-sm font-medium text-gray-700"
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {/* Use filteredReservations instead of reservations */}
                    {filteredReservations.slice(0, Math.min(filteredReservations.length, 50)).map((resv, idx) => (
                      <tr
                        key={resv.id}
                        className={idx % 2 === 0 ? "bg-white" : "bg-gray-50"}
                      >
                        <td className="border border-gray-300 p-2 text-sm">{resv.id}</td>
                        <td className="border border-gray-300 p-2 text-sm">{resv.logement?.nom_logement || "—"}</td>
                        <td className="border border-gray-300 p-2 text-sm">{resv.nom_client || "—"}</td>
                        <td className="border border-gray-300 p-2 text-sm">
                          {resv.date_arrivee ? new Date(resv.date_arrivee).toLocaleDateString() : "—"}
                        </td>
                        <td className="border border-gray-300 p-2 text-sm">
                          {resv.date_depart ? new Date(resv.date_depart).toLocaleDateString() : "—"}
                        </td>
                        <td className="border border-gray-300 p-2 text-sm">{resv.statut || "—"}</td>
                        <td className="border border-gray-300 p-2 text-sm">{resv.montant_total ? `${resv.montant_total} €` : "—"}</td>
                        <td className="border border-gray-300 p-2 text-sm">{resv.source_plateforme || "—"}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </CardContent>
      </Card>
      {/* Suppression du bouton "Supprimer Direct" */}
    </div>
  );
};

export default ImportTab;
