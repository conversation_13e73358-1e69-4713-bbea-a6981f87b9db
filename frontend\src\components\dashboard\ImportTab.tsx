import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  Trash2,
  Building2,
  Filter,
  Loader2,
  Upload,
  AlertCircle,
  CheckCircle,
  FileText,
  Info,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { FileUpload } from '@/components/FileUpload';
import { useImportReservationsMutation } from '@/features/api/reservationsApi';

// Add this component at the top level of your file
const DeleteReservationsButton = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const handleDelete = async () => {
    console.log("Delete button clicked");
    
    try {
      toast({
        title: "Suppression en cours",
        description: "Veuillez patienter...",
      });
      
      const response = await fetch('http://localhost:3000/api/reservations/all', {
        method: 'DELETE',
      });
      
      console.log("Delete API response:", response.status);
      
      if (response.ok) {
        toast({
          title: "Succès",
          description: "Toutes les réservations ont été supprimées",
        });
        
        // Force page reload to reflect changes
        window.location.reload();
      } else {
        throw new Error("Échec de la suppression");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast({
        title: "Erreur",
        description: "Impossible de supprimer les réservations",
        variant: "destructive",
      });
    }
  };
  
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" className="flex items-center gap-2">
          <Trash2 className="h-4 w-4" />
          Supprimer
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
          <AlertDialogDescription>
            Cette action supprimera définitivement toutes les réservations. Cette action ne peut pas être annulée.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Annuler</AlertDialogCancel>
          <Button variant="destructive" onClick={handleDelete}>
            Supprimer
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

interface Reservation {
  plateforme: string;
  id: string | number;
  logement?: {
    nom_logement?: string;
  };
  nom_client?: string;
  date_arrivee?: string;
  date_depart?: string;
  statut?: string;
  montant_total?: number;
  source_plateforme?: string;
}

export const ImportTab = () => {
  const { toast } = useToast();
  const navigate = useNavigate();

  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [filteredReservations, setFilteredReservations] = useState<Reservation[]>([]);
  const [loadingReservations, setLoadingReservations] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(20);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [platformFilter, setPlatformFilter] = useState<string>("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // États pour la vérification de fichiers
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isFileValid, setIsFileValid] = useState(false);
  const [verificationErrors, setVerificationErrors] = useState<string[]>([]);
  const [verificationWarnings, setVerificationWarnings] = useState<string[]>([]);

  const [importReservations, { isLoading: isImporting }] = useImportReservationsMutation();

  const fetchReservations = useCallback(async (pageToFetch = 1) => {
    setLoadingReservations(true);
    setFetchError(null);

    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      const res = await fetch(
        `http://localhost:3000/api/reservations?page=${pageToFetch}&limit=${limit}`, 
        { signal: controller.signal }
      );
      
      clearTimeout(timeoutId);
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      const data = await res.json();

      if (data.success) {
        setReservations(data.data);
        setPage(data.pagination.page);
        setTotalPages(data.pagination.pages);
      } else {
        setFetchError(data.message || "Erreur lors du chargement des réservations");
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          setFetchError("La requête a pris trop de temps. Veuillez réessayer.");
        } else {
          setFetchError("Erreur serveur: " + err.message);
        }
      } else {
        setFetchError("Erreur serveur inconnue");
      }
    } finally {
      setLoadingReservations(false);
    }
  }, [limit]); // Only depend on limit to prevent unnecessary re-renders

  // Apply filter when platformFilter or reservations change
  useEffect(() => {
    if (platformFilter === "all") {
      setFilteredReservations(reservations);
    } else {
      setFilteredReservations(
        reservations.filter(
          (resv) => resv.source_plateforme?.toLowerCase() === platformFilter.toLowerCase()
        )
      );
    }
  }, [platformFilter, reservations]);

  useEffect(() => {
    fetchReservations(page);
  }, [page, fetchReservations]);

  // Fonction pour sélectionner un fichier (avec vérification basique)
  const handleFileSelect = async (file: File) => {
    setUploadError(null);

    // Validation basique du fichier
    const validExtensions = ['.csv', '.xls', '.xlsx'];
    const fileExt = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    if (!validExtensions.includes(fileExt)) {
      const errorMsg = "Format de fichier non supporté. Veuillez télécharger un fichier CSV ou Excel (.csv, .xls, .xlsx).";
      setUploadError(errorMsg);
      toast({
        title: "❌ Format invalide",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    // Validation de la taille
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      const errorMsg = "La taille du fichier ne doit pas dépasser 10MB.";
      setUploadError(errorMsg);
      toast({
        title: "❌ Fichier trop volumineux",
        description: errorMsg,
        variant: "destructive",
      });
      return;
    }

    // Si les validations passent, sélectionner le fichier pour vérification
    setSelectedFile(file);

    toast({
      title: "📁 Fichier sélectionné",
      description: `"${file.name}" prêt pour vérification`,
    });
  };

  // Fonction de callback pour la vérification
  const handleVerificationComplete = (isValid: boolean, errors: string[], warnings: string[]) => {
    setIsFileValid(isValid);
    setVerificationErrors(errors);
    setVerificationWarnings(warnings);
  };

  // Fonction pour procéder à l'import après vérification
  const handleProceedWithImport = async () => {
    if (!selectedFile || !isFileValid) return;

    setUploadError(null);

    // Notification de début d'importation
    toast({
      title: "🚀 Début de l'importation",
      description: `Traitement du fichier "${selectedFile.name}" en cours...`,
    });

    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const result = await importReservations(formData).unwrap();

      // Notification de succès détaillée
      const insertedCount = result.results?.inserted || 0;
      const updatedCount = result.results?.updated || 0;
      const totalCount = insertedCount + updatedCount;

      toast({
        title: "🎉 Import terminé avec succès !",
        description: `📁 Fichier: ${selectedFile.name}
📊 ${totalCount} réservations traitées
➕ ${insertedCount} nouvelles réservations
🔄 ${updatedCount} réservations mises à jour
🏠 Logements créés automatiquement`,
      });

      // Notification supplémentaire pour les sections mises à jour
      setTimeout(() => {
        toast({
          title: "🔄 Synchronisation terminée",
          description: "Les sections Logements, Réservations et IA ont été mises à jour automatiquement",
        });
      }, 2000);

      // Clear states
      setUploadError(null);
      setSelectedFile(null);
      setIsFileValid(false);

      // Refresh the reservations list
      setPage(1);
      fetchReservations(1);
    } catch (error: unknown) {
      // Type guard to safely access error properties
      if (error && typeof error === 'object' && 'data' in error) {
        const apiError = error as { data?: { message?: string } };
        const errorMsg = apiError.data?.message || "Une erreur est survenue lors de l'importation.";
        setUploadError(errorMsg);

        toast({
          title: "❌ Erreur d'importation",
          description: `📁 Fichier: ${selectedFile.name}
⚠️ ${errorMsg}
💡 Vérifiez le format de votre fichier`,
          variant: "destructive",
        });
      } else {
        // Fallback for other error types
        const errorMsg = "Une erreur inattendue est survenue";
        setUploadError(errorMsg);

        toast({
          title: "❌ Erreur inattendue",
          description: `📁 Fichier: ${selectedFile.name}
⚠️ ${errorMsg}
🔄 Veuillez réessayer`,
          variant: "destructive",
        });
      }
    }
  };

  // Fonction simplifiée pour supprimer toutes les réservations
  const handleDeleteAllReservations = async () => {
    try {
      console.log("Tentative de suppression de toutes les réservations...");

      // Notification de début de suppression
      toast({
        title: "🗑️ Suppression en cours",
        description: "Suppression de toutes les réservations en cours...",
      });

      const response = await fetch('http://localhost:3000/api/reservations/all', {
        method: 'DELETE'
      });

      console.log("Réponse:", response.status);

      if (response.ok) {
        // Notification de succès détaillée
        toast({
          title: "🎉 Suppression terminée",
          description: `✅ Toutes les réservations ont été supprimées
🔄 Les analyses IA vont se mettre à jour
📊 Les statistiques seront recalculées`,
        });

        // Notification supplémentaire pour les sections affectées
        setTimeout(() => {
          toast({
            title: "🔄 Mise à jour automatique",
            description: "Les sections Logements, Réservations et IA reflètent maintenant les changements",
          });
        }, 2000);

        window.location.reload(); // Recharger la page
      } else {
        // Essayer de lire le texte de la réponse au lieu du JSON
        const errorText = await response.text();
        console.error("Erreur texte:", errorText);
        throw new Error("Erreur lors de la suppression (statut " + response.status + ")");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast({
        title: "❌ Erreur de suppression",
        description: `⚠️ Impossible de supprimer les réservations
🔍 Détails: ${error instanceof Error ? error.message : "Erreur inconnue"}
🔄 Veuillez réessayer`,
        variant: "destructive",
      });
    }
  };

  // Suppression de la fonction testDeleteAPI inutile
  // function testDeleteAPI(event: React.MouseEvent<HTMLButtonElement>): void {
  //   throw new Error('Function not implemented.');
  // }

  // Suppression du code commenté pour testDeleteAPI

  // Créons un nouveau composant de bouton de suppression qui fonctionne correctement
  const TruncateReservationsButton = () => {
    const { toast } = useToast();
    
    const handleTruncate = async () => {
      try {
        // Afficher un toast de chargement
        toast({
          title: "Suppression en cours",
          description: "Vidage de la table des réservations...",
        });
        
        // Appel à l'API pour effectuer un TRUNCATE de la table
        const response = await fetch('http://localhost:3000/api/reservations/all', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-Truncate-Table': 'true' // En-tête spécial pour indiquer un truncate
          }
        });
        
        if (!response.ok) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }
        
        // Succès
        toast({
          title: "Table vidée",
          description: "Toutes les réservations ont été supprimées de la base de données",
        });
        
        // Recharger la page pour actualiser l'affichage
        window.location.reload();
      } catch (error) {
        console.error("Erreur lors du truncate:", error);
        toast({
          title: "Erreur",
          description: error instanceof Error ? error.message : "Erreur lors de la suppression",
          variant: "destructive",
        });
      }
    };
    
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="destructive" className="flex items-center gap-2">
            <Trash2 className="h-4 w-4" />
            Vider la table
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Vider la table des réservations ?</AlertDialogTitle>
            <AlertDialogDescription>
              Cette action va supprimer TOUTES les réservations de la base de données (TRUNCATE). 
              Cette opération est irréversible.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annuler</AlertDialogCancel>
            <Button variant="destructive" onClick={handleTruncate}>
              Vider la table
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <div className="space-y-8 animate-fade-in max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Upload Section */}
      <Card className="lg:col-span-2 shadow-xl rounded-2xl border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6">
          <CardTitle className="flex items-center gap-3 text-xl font-bold">
            <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
              <Upload className="w-6 h-6" />
            </div>
            📥 Importer des fichiers de réservations
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <FileUpload
            onFileSelect={handleFileSelect}
            isLoading={isImporting}
            error={uploadError}
          />
        </CardContent>
      </Card>

      {/* File Verification Section */}
      {selectedFile && (
        <Card className="shadow-xl rounded-2xl border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                <AlertCircle className="w-6 h-6" />
              </div>
              🔍 Vérification du fichier
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            {/* Informations du fichier */}
            <div className="flex items-start gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-xl">
              <FileText className="w-8 h-8 text-blue-500" />
              <div className="flex-1">
                <h3 className="font-semibold text-slate-800 dark:text-slate-200">{selectedFile.name}</h3>
                <div className="grid grid-cols-2 gap-4 mt-2 text-sm text-slate-600 dark:text-slate-400">
                  <div>📁 Taille: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</div>
                  <div>📅 Modifié: {new Date(selectedFile.lastModified).toLocaleString('fr-FR')}</div>
                </div>
              </div>
            </div>

            {/* Statut de validation */}
            <div className="flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-green-500" />
              <span className="font-semibold text-lg">✅ Fichier valide</span>
            </div>

            {/* Recommandations */}
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl">
              <h4 className="font-semibold text-blue-800 dark:text-blue-400 mb-2 flex items-center gap-2">
                <Info className="w-4 h-4" />
                💡 Recommandations
              </h4>
              <ul className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                <li>• Vérifiez que les dates sont au format DD/MM/YYYY ou YYYY-MM-DD</li>
                <li>• Assurez-vous que les montants sont au format numérique (ex: 123.45)</li>
                <li>• Les noms de colonnes peuvent être en français ou anglais</li>
                <li>• ✅ Votre fichier respecte les critères de base !</li>
              </ul>
            </div>

            {/* Bouton d'import */}
            <div className="flex gap-3">
              <Button
                onClick={handleProceedWithImport}
                disabled={isImporting}
                className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                {isImporting ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <Upload className="w-4 h-4" />
                )}
                {isImporting ? '⏳ Import en cours...' : '🚀 Procéder à l\'import'}
              </Button>

              <Button
                variant="outline"
                onClick={() => setSelectedFile(null)}
                className="flex items-center gap-2"
              >
                ❌ Annuler
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reservations Table with Filter */}
      <Card className="shadow-xl rounded-2xl border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                <Building2 className="w-6 h-6" />
              </div>
              📋 Liste des réservations
            </CardTitle>

            <div className="flex flex-wrap gap-3">
              {/* Platform Filter */}
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger className="w-[200px] bg-white/10 border-white/20 text-white backdrop-blur-sm hover:bg-white/20 transition-all">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <SelectValue placeholder="Filtrer par plateforme" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">🌐 Toutes les plateformes</SelectItem>
                  <SelectItem value="airbnb">🏠 Airbnb</SelectItem>
                  <SelectItem value="booking">🏨 Booking</SelectItem>
                </SelectContent>
              </Select>

              {/* Bouton direct sans confirmation */}
              <Button
                variant="destructive"
                className="flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white font-medium px-4 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                onClick={handleDeleteAllReservations}
              >
                <Trash2 className="h-4 w-4" />
                🗑️ Supprimer
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <div className="px-6 py-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-800 border-b border-slate-200 dark:border-slate-600">
          <div className="flex justify-between items-center">
            <Button
              onClick={() => setPage((p) => Math.max(p - 1, 1))}
              disabled={page <= 1}
              variant="outline"
              className="bg-white hover:bg-slate-50 border-slate-300 text-slate-700 font-medium px-4 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50"
              aria-label="Page précédente"
            >
              ⬅️ Précédent
            </Button>
            <div className="flex items-center gap-3 bg-white dark:bg-slate-800 px-4 py-2 rounded-xl shadow-sm border border-slate-200 dark:border-slate-600">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                📄 Page {page} / {totalPages}
              </span>
            </div>
            <Button
              onClick={() => setPage((p) => (p < totalPages ? p + 1 : p))}
              disabled={page >= totalPages}
              variant="outline"
              className="bg-white hover:bg-slate-50 border-slate-300 text-slate-700 font-medium px-4 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50"
              aria-label="Page suivante"
            >
              Suivant ➡️
            </Button>
          </div>
        </div>
        
        <CardContent className="p-0">
          {loadingReservations ? (
            <div className="flex justify-center items-center gap-3 text-slate-600 font-medium py-12">
              <Loader2 className="animate-spin w-6 h-6 text-indigo-500" />
              <span className="text-lg">⏳ Chargement des réservations...</span>
            </div>
          ) : fetchError ? (
            <div className="text-center space-y-4 py-12">
              <div className="text-red-600 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mx-6">
                <p className="font-medium">❌ {fetchError}</p>
              </div>
              <Button
                variant="outline"
                onClick={() => fetchReservations(page)}
                className="bg-white hover:bg-slate-50 border-slate-300 text-slate-700 font-medium px-6 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
              >
                🔄 Réessayer
              </Button>
            </div>
          ) : filteredReservations.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-xl p-6 mx-6">
                <p className="text-slate-500 dark:text-slate-400 font-medium">
                  📭 {reservations.length === 0
                    ? "Aucune réservation trouvée."
                    : `Aucune réservation trouvée pour la plateforme "${platformFilter}".`}
                </p>
              </div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full min-w-[800px]">
                  <thead className="bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 sticky top-0 z-10">
                    <tr>
                      {[
                        { key: "ID", icon: "🆔", label: "ID" },
                        { key: "Logement", icon: "🏠", label: "Logement" },
                        { key: "Nom client", icon: "👤", label: "Nom client" },
                        { key: "Date arrivée", icon: "📅", label: "Date arrivée" },
                        { key: "Date départ", icon: "📅", label: "Date départ" },
                        { key: "Statut", icon: "📊", label: "Statut" },
                        { key: "Montant total", icon: "💰", label: "Montant total" },
                        { key: "Plateforme", icon: "🌐", label: "Plateforme" },
                      ].map((header) => (
                        <th
                          key={header.key}
                          className="px-6 py-4 text-left text-sm font-bold text-slate-700 dark:text-slate-300 border-b-2 border-slate-300 dark:border-slate-600"
                        >
                          <div className="flex items-center gap-2">
                            <span>{header.icon}</span>
                            <span>{header.label}</span>
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {/* Use filteredReservations instead of reservations */}
                    {filteredReservations.slice(0, Math.min(filteredReservations.length, 50)).map((resv, idx) => {
                      const getStatusBadge = (status: string) => {
                        const statusConfig = {
                          'OK': { bg: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400', icon: '✅' },
                          'Ancien voyageur': { bg: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400', icon: '👤' },
                          'Annulé': { bg: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400', icon: '❌' },
                        };
                        const config = statusConfig[status as keyof typeof statusConfig] || { bg: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400', icon: '📋' };
                        return (
                          <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${config.bg}`}>
                            <span>{config.icon}</span>
                            {status || "—"}
                          </span>
                        );
                      };

                      const getPlatformBadge = (platform: string) => {
                        const platformConfig = {
                          'airbnb': { bg: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400', icon: '🏠' },
                          'booking': { bg: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400', icon: '🏨' },
                        };
                        const config = platformConfig[platform as keyof typeof platformConfig] || { bg: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400', icon: '🌐' };
                        return (
                          <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${config.bg}`}>
                            <span>{config.icon}</span>
                            {platform || "—"}
                          </span>
                        );
                      };

                      return (
                        <tr
                          key={resv.id}
                          className={`${
                            idx % 2 === 0
                              ? "bg-white dark:bg-slate-800"
                              : "bg-slate-50 dark:bg-slate-700"
                          } hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors duration-200`}
                        >
                          <td className="px-6 py-4 text-sm font-medium text-slate-900 dark:text-slate-100 border-b border-slate-200 dark:border-slate-600">
                            <span className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 px-2 py-1 rounded-lg text-xs font-bold">
                              #{resv.id}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-600">
                            <div className="font-medium">{resv.logement?.nom_logement || "—"}</div>
                          </td>
                          <td className="px-6 py-4 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-600">
                            <div className="flex items-center gap-2">
                              <span>👤</span>
                              <span className="font-medium">{resv.nom_client || "—"}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-600">
                            <div className="flex items-center gap-2">
                              <span>📅</span>
                              <span>{resv.date_arrivee ? new Date(resv.date_arrivee).toLocaleDateString('fr-FR') : "—"}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-600">
                            <div className="flex items-center gap-2">
                              <span>📅</span>
                              <span>{resv.date_depart ? new Date(resv.date_depart).toLocaleDateString('fr-FR') : "—"}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm border-b border-slate-200 dark:border-slate-600">
                            {getStatusBadge(resv.statut)}
                          </td>
                          <td className="px-6 py-4 text-sm border-b border-slate-200 dark:border-slate-600">
                            <div className="flex items-center gap-2">
                              <span>💰</span>
                              <span className="font-bold text-emerald-600 dark:text-emerald-400">
                                {resv.montant_total ? `${resv.montant_total} €` : "—"}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm border-b border-slate-200 dark:border-slate-600">
                            {getPlatformBadge(resv.source_plateforme)}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </CardContent>
      </Card>
      {/* Suppression du bouton "Supprimer Direct" */}
    </div>
  );
};

export default ImportTab;
