import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  FileText, 
  FileSpreadsheet,
  Eye,
  Upload,
  RefreshCw,
  AlertCircle,
  Info
} from 'lucide-react';

interface FileVerificationProps {
  file: File | null;
  onVerificationComplete: (isValid: boolean, errors: string[], warnings: string[]) => void;
  onProceedWithImport: () => void;
  isImporting: boolean;
}

interface VerificationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    name: string;
    size: string;
    type: string;
    lastModified: string;
  };
  preview: string[][];
  stats: {
    totalRows: number;
    totalColumns: number;
    emptyRows: number;
    duplicateRows: number;
  };
}

export const FileVerification: React.FC<FileVerificationProps> = ({
  file,
  onVerificationComplete,
  onProceedWithImport,
  isImporting
}) => {
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [verificationProgress, setVerificationProgress] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    if (file) {
      verifyFile();
    } else {
      setVerificationResult(null);
    }
  }, [file]);

  const verifyFile = async () => {
    if (!file) return;

    setIsVerifying(true);
    setVerificationProgress(0);

    try {
      // Simulation du processus de vérification avec progression
      const steps = [
        { name: 'Lecture du fichier', duration: 500 },
        { name: 'Validation du format', duration: 300 },
        { name: 'Analyse des colonnes', duration: 400 },
        { name: 'Vérification des données', duration: 600 },
        { name: 'Détection des doublons', duration: 400 }
      ];

      let currentProgress = 0;
      for (const step of steps) {
        toast({
          title: "🔍 Vérification en cours",
          description: step.name,
        });
        
        await new Promise(resolve => setTimeout(resolve, step.duration));
        currentProgress += 100 / steps.length;
        setVerificationProgress(currentProgress);
      }

      // Lecture et analyse du fichier
      const result = await analyzeFile(file);
      setVerificationResult(result);
      onVerificationComplete(result.isValid, result.errors, result.warnings);

      if (result.isValid) {
        toast({
          title: "✅ Vérification réussie",
          description: `Fichier valide avec ${result.stats.totalRows} lignes détectées`,
        });
      } else {
        toast({
          title: "❌ Erreurs détectées",
          description: `${result.errors.length} erreur(s) trouvée(s)`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "❌ Erreur de vérification",
        description: "Impossible de vérifier le fichier",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
      setVerificationProgress(100);
    }
  };

  const analyzeFile = async (file: File): Promise<VerificationResult> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const lines = content.split('\n').filter(line => line.trim());
        
        // Analyse basique du contenu
        const errors: string[] = [];
        const warnings: string[] = [];
        
        // Vérifications de base
        if (lines.length < 2) {
          errors.push("Le fichier doit contenir au moins un en-tête et une ligne de données");
        }
        
        if (file.size > 10 * 1024 * 1024) {
          warnings.push("Fichier volumineux (>10MB) - l'importation peut prendre du temps");
        }
        
        // Analyse avancée des colonnes
        const headers = lines[0]?.split(',').map(h => h.trim().replace(/"/g, '')) || [];

        // Colonnes obligatoires et recommandées
        const requiredColumns = [
          { name: 'nom_client', alternatives: ['client', 'guest', 'nom', 'name'], required: true },
          { name: 'date_arrivee', alternatives: ['checkin', 'arrival', 'arrivee', 'start'], required: true },
          { name: 'date_depart', alternatives: ['checkout', 'departure', 'depart', 'end'], required: true },
          { name: 'montant_total', alternatives: ['total', 'amount', 'price', 'prix', 'montant'], required: false }
        ];

        const columnAnalysis = requiredColumns.map(col => {
          const found = headers.find(header =>
            header.toLowerCase().includes(col.name.toLowerCase()) ||
            col.alternatives.some(alt => header.toLowerCase().includes(alt.toLowerCase()))
          );
          return { ...col, found: !!found, matchedHeader: found };
        });

        const missingRequired = columnAnalysis.filter(col => col.required && !col.found);
        const missingRecommended = columnAnalysis.filter(col => !col.required && !col.found);

        if (missingRequired.length > 0) {
          errors.push(`Colonnes obligatoires manquantes: ${missingRequired.map(c => c.name).join(', ')}`);
        }

        if (missingRecommended.length > 0) {
          warnings.push(`Colonnes recommandées manquantes: ${missingRecommended.map(c => c.name).join(', ')}`);
        }

        // Vérification du format des données
        if (lines.length > 1) {
          const sampleRow = lines[1].split(',');

          // Vérification des dates
          const dateColumns = columnAnalysis.filter(col => col.name.includes('date') && col.found);
          dateColumns.forEach(dateCol => {
            const colIndex = headers.findIndex(h => h === dateCol.matchedHeader);
            if (colIndex >= 0 && sampleRow[colIndex]) {
              const dateValue = sampleRow[colIndex].trim().replace(/"/g, '');
              const dateFormats = [
                /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
                /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
                /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
              ];

              const isValidDate = dateFormats.some(format => format.test(dateValue)) || !isNaN(Date.parse(dateValue));
              if (!isValidDate && dateValue !== '') {
                warnings.push(`Format de date suspect dans la colonne "${dateCol.matchedHeader}": ${dateValue}`);
              }
            }
          });

          // Vérification des montants
          const amountCol = columnAnalysis.find(col => col.name === 'montant_total' && col.found);
          if (amountCol) {
            const colIndex = headers.findIndex(h => h === amountCol.matchedHeader);
            if (colIndex >= 0 && sampleRow[colIndex]) {
              const amountValue = sampleRow[colIndex].trim().replace(/"/g, '');
              const isValidAmount = /^\d+([.,]\d{2})?$/.test(amountValue) || !isNaN(parseFloat(amountValue));
              if (!isValidAmount && amountValue !== '') {
                warnings.push(`Format de montant suspect dans la colonne "${amountCol.matchedHeader}": ${amountValue}`);
              }
            }
          }
        }
        
        // Détection des doublons (simulation)
        const duplicateRows = Math.floor(lines.length * 0.02); // 2% de doublons simulés
        if (duplicateRows > 0) {
          warnings.push(`${duplicateRows} ligne(s) potentiellement dupliquée(s) détectée(s)`);
        }
        
        // Lignes vides
        const emptyRows = Math.floor(lines.length * 0.01); // 1% de lignes vides simulées
        if (emptyRows > 0) {
          warnings.push(`${emptyRows} ligne(s) vide(s) détectée(s)`);
        }
        
        // Prévisualisation (premières 5 lignes)
        const preview = lines.slice(0, 5).map(line => line.split(','));
        
        const result: VerificationResult = {
          isValid: errors.length === 0,
          errors,
          warnings,
          fileInfo: {
            name: file.name,
            size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
            type: file.type || 'text/csv',
            lastModified: new Date(file.lastModified).toLocaleString('fr-FR')
          },
          preview,
          stats: {
            totalRows: lines.length - 1, // Exclure l'en-tête
            totalColumns: headers.length,
            emptyRows,
            duplicateRows
          }
        };
        
        resolve(result);
      };
      
      reader.readAsText(file);
    });
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (extension === 'csv') {
      return <FileText className="w-8 h-8 text-blue-500" />;
    } else if (['xls', 'xlsx'].includes(extension || '')) {
      return <FileSpreadsheet className="w-8 h-8 text-green-500" />;
    }
    return <FileText className="w-8 h-8 text-gray-500" />;
  };

  if (!file) {
    return null;
  }

  return (
    <Card className="shadow-xl rounded-2xl border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6">
        <CardTitle className="flex items-center gap-3 text-xl font-bold">
          <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
            <AlertCircle className="w-6 h-6" />
          </div>
          🔍 Vérification du fichier
        </CardTitle>
      </CardHeader>

      <CardContent className="p-6 space-y-6">
        {/* Informations du fichier */}
        <div className="flex items-start gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-xl">
          {getFileIcon(file.name)}
          <div className="flex-1">
            <h3 className="font-semibold text-slate-800 dark:text-slate-200">{file.name}</h3>
            {verificationResult && (
              <div className="grid grid-cols-2 gap-4 mt-2 text-sm text-slate-600 dark:text-slate-400">
                <div>📁 Taille: {verificationResult.fileInfo.size}</div>
                <div>📅 Modifié: {verificationResult.fileInfo.lastModified}</div>
                <div>📊 Lignes: {verificationResult.stats.totalRows}</div>
                <div>📋 Colonnes: {verificationResult.stats.totalColumns}</div>
              </div>
            )}
          </div>
        </div>

        {/* Progression de vérification */}
        {isVerifying && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />
              <span className="text-sm font-medium">Vérification en cours...</span>
            </div>
            <Progress value={verificationProgress} className="h-2" />
          </div>
        )}

        {/* Résultats de vérification */}
        {verificationResult && !isVerifying && (
          <div className="space-y-4">
            {/* Statut global avec statistiques */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {verificationResult.isValid ? (
                  <CheckCircle className="w-6 h-6 text-green-500" />
                ) : (
                  <XCircle className="w-6 h-6 text-red-500" />
                )}
                <span className="font-semibold text-lg">
                  {verificationResult.isValid ? '✅ Fichier valide' : '❌ Erreurs détectées'}
                </span>
              </div>

              <div className="flex gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  <span>{verificationResult.errors.length} erreur(s)</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span>{verificationResult.warnings.length} avertissement(s)</span>
                </div>
              </div>
            </div>

            {/* Erreurs */}
            {verificationResult.errors.length > 0 && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
                <h4 className="font-semibold text-red-800 dark:text-red-400 mb-2 flex items-center gap-2">
                  <XCircle className="w-4 h-4" />
                  Erreurs ({verificationResult.errors.length})
                </h4>
                <ul className="space-y-1">
                  {verificationResult.errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-700 dark:text-red-300">
                      • {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Avertissements */}
            {verificationResult.warnings.length > 0 && (
              <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-400 mb-2 flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4" />
                  Avertissements ({verificationResult.warnings.length})
                </h4>
                <ul className="space-y-1">
                  {verificationResult.warnings.map((warning, index) => (
                    <li key={index} className="text-sm text-yellow-700 dark:text-yellow-300">
                      • {warning}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Recommandations */}
            {(verificationResult.warnings.length > 0 || verificationResult.errors.length === 0) && (
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl">
                <h4 className="font-semibold text-blue-800 dark:text-blue-400 mb-2 flex items-center gap-2">
                  <Info className="w-4 h-4" />
                  💡 Recommandations
                </h4>
                <ul className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                  <li>• Vérifiez que les dates sont au format DD/MM/YYYY ou YYYY-MM-DD</li>
                  <li>• Assurez-vous que les montants sont au format numérique (ex: 123.45)</li>
                  <li>• Les noms de colonnes peuvent être en français ou anglais</li>
                  <li>• Évitez les caractères spéciaux dans les noms de clients</li>
                  {verificationResult.warnings.length === 0 && (
                    <li>• ✅ Votre fichier respecte toutes les bonnes pratiques !</li>
                  )}
                </ul>
              </div>
            )}

            {/* Boutons d'action */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowPreview(!showPreview)}
                className="flex items-center gap-2 bg-white hover:bg-slate-50 border-slate-300 text-slate-700 font-medium px-4 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
              >
                <Eye className="w-4 h-4" />
                {showPreview ? '👁️ Masquer' : '👁️ Prévisualiser'} le contenu
              </Button>

              {verificationResult.isValid && (
                <Button
                  onClick={onProceedWithImport}
                  disabled={isImporting}
                  className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  {isImporting ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4" />
                  )}
                  {isImporting ? '⏳ Import en cours...' : '🚀 Procéder à l\'import'}
                </Button>
              )}

              {!verificationResult.isValid && (
                <Button
                  variant="outline"
                  disabled
                  className="flex items-center gap-2 opacity-50 cursor-not-allowed"
                >
                  <XCircle className="w-4 h-4" />
                  ❌ Corrigez les erreurs avant d'importer
                </Button>
              )}
            </div>

            {/* Prévisualisation */}
            {showPreview && verificationResult.preview.length > 0 && (
              <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-xl">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  Aperçu du contenu (5 premières lignes)
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <tbody>
                      {verificationResult.preview.map((row, rowIndex) => (
                        <tr key={rowIndex} className={rowIndex === 0 ? 'font-semibold bg-slate-200 dark:bg-slate-700' : ''}>
                          {row.map((cell, cellIndex) => (
                            <td key={cellIndex} className="px-3 py-2 border border-slate-300 dark:border-slate-600 truncate max-w-32">
                              {cell || '—'}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
