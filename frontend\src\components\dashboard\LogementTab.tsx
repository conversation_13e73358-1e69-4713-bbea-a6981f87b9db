import React, { useState, useEffect } from 'react';
import { Building2, Search, Filter, Plus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

type Logement = {
  id: number;
  nom_logement: string;
  plateforme: string;
  statut?: string;
  adresse?: string;
  ville?: string;
  capacite?: number;
};

export const LogementTab = ({ isAdmin }: { isAdmin: boolean }) => {
  const [logements, setLogements] = useState<Logement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [platformFilter, setPlatformFilter] = useState('all');
  const { toast } = useToast();

  useEffect(() => {
    const fetchLogements = async () => {
      try {
        setLoading(true);
        // Replace with your actual API endpoint
        const response = await fetch('/api/logements');
        
        if (!response.ok) {
          throw new Error('Erreur lors du chargement des logements');
        }
        
        const data = await response.json();
        setLogements(data.data || []);
      } catch (err) {
        setError('Impossible de charger les logements');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchLogements();
  }, []);

  // For demo purposes, if API fails or during development
  useEffect(() => {
    if (logements.length === 0 && !loading) {
      setLogements([
        {
          id: 1,
          nom_logement: "Appartement Centre-ville",
          plateforme: "airbnb",
          statut: "Actif",
          adresse: "123 Rue de Paris",
          ville: "Paris",
          capacite: 4
        },
        {
          id: 2,
          nom_logement: "Maison avec jardin",
          plateforme: "booking",
          statut: "Actif",
          adresse: "45 Avenue des Fleurs",
          ville: "Lyon",
          capacite: 6
        },
        {
          id: 3,
          nom_logement: "Studio moderne",
          plateforme: "airbnb",
          statut: "Maintenance",
          adresse: "78 Boulevard Maritime",
          ville: "Marseille",
          capacite: 2
        },
      ]);
    }
  }, [loading, logements]);

  const filteredLogements = logements.filter(logement => {
    const matchesSearch = logement.nom_logement.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          logement.ville?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPlatform = platformFilter === 'all' || logement.plateforme === platformFilter;
    
    return matchesSearch && matchesPlatform;
  });

  const getPlatformColor = (platform: string) => {
    return platform === 'airbnb' ? 'bg-[#FF5A5F] text-white' : 'bg-[#003580] text-white';
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header avec filtres et recherche */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold">Gestion des logements</h3>
          <p className="text-muted-foreground">Consultez et gérez vos propriétés</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Rechercher un logement..."
              className="pl-8 w-full sm:w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select value={platformFilter} onValueChange={setPlatformFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Plateforme" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les plateformes</SelectItem>
              <SelectItem value="airbnb">Airbnb</SelectItem>
              <SelectItem value="booking">Booking.com</SelectItem>
            </SelectContent>
          </Select>
          
          {isAdmin && (
            <Button className="gap-2 bg-gradient-to-r from-rent-blue-500 to-rent-purple-600 hover:from-rent-blue-600 hover:to-rent-purple-700">
              <Plus className="w-4 h-4" />
              Ajouter
            </Button>
          )}
        </div>
      </div>

      {/* Affichage des logements */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p>Chargement des logements...</p>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">{error}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLogements.length > 0 ? (
            filteredLogements.map((logement) => (
              <Card key={logement.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <Building2 className="w-5 h-5 text-rent-blue-600" />
                      <CardTitle className="text-lg">{logement.nom_logement}</CardTitle>
                    </div>
                    <Badge 
                      variant="outline"
                      className={getPlatformColor(logement.plateforme)}
                    >
                      {logement.plateforme === 'airbnb' ? 'Airbnb' : 'Booking.com'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {logement.adresse && (
                    <div className="text-sm">
                      <span className="font-medium">Adresse:</span> {logement.adresse}, {logement.ville}
                    </div>
                  )}
                  {logement.capacite && (
                    <div className="text-sm">
                      <span className="font-medium">Capacité:</span> {logement.capacite} personnes
                    </div>
                  )}
                  <div className="text-sm">
                    <span className="font-medium">Statut:</span>{' '}
                    <Badge 
                      variant={logement.statut === "Actif" ? "default" : "secondary"}
                      className={logement.statut === "Actif" ? "bg-green-100 text-green-800" : ""}
                    >
                      {logement.statut || 'Actif'}
                    </Badge>
                  </div>
                  
                  <div className="pt-2 flex gap-2">
                    <Button variant="outline" size="sm" className="w-full">
                      Détails
                    </Button>
                    {isAdmin && (
                      <Button variant="outline" size="sm" className="w-full">
                        Modifier
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-10">
              <p className="text-muted-foreground">Aucun logement trouvé.</p>
            </div>
          )}
        </div>
      )}

      {/* Statistiques des logements */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Répartition par plateforme</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-[#FF5A5F]"></div>
                  <span>Airbnb</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.plateforme === 'airbnb').length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-[#003580]"></div>
                  <span>Booking.com</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.plateforme === 'booking').length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Statut des logements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>Actifs</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.statut === 'Actif').length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span>En maintenance</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.statut === 'Maintenance').length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Capacité totale</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {logements.reduce((sum, logement) => sum + (logement.capacite || 0), 0)}
            </div>
            <p className="text-sm text-muted-foreground">personnes au total</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};