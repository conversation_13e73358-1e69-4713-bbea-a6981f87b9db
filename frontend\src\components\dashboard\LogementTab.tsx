import React, { useState } from 'react';
import { Building2, Search, Plus, Edit, Trash2, Camera, Eye, BarChart3 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  useGetAllLogementsQuery,
  useDeleteLogementMutation,
  Logement
} from '@/features/api/logementsApi';
import { LogementModal } from './LogementModal';
import { PhotoModal } from './PhotoModal';
import { StatsModal } from './StatsModal';

export const LogementTab = ({ isAdmin }: { isAdmin: boolean }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [platformFilter, setPlatformFilter] = useState('all');
  const [selectedLogement, setSelectedLogement] = useState<Logement | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPhotoModalOpen, setIsPhotoModalOpen] = useState(false);
  const [isStatsModalOpen, setIsStatsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  const { toast } = useToast();

  // Récupérer les logements avec les filtres
  const {
    data: logementsResponse,
    isLoading: loading,
    error,
    refetch
  } = useGetAllLogementsQuery({
    search: searchTerm || undefined,
    plateforme: platformFilter === 'all' ? undefined : platformFilter
  });

  const [deleteLogement] = useDeleteLogementMutation();

  const logements = logementsResponse?.data || [];

  const getPlatformColor = (platform: string) => {
    return platform === 'airbnb' ? 'bg-[#FF5A5F] text-white' : 'bg-[#003580] text-white';
  };

  const handleCreateLogement = () => {
    setSelectedLogement(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleEditLogement = (logement: Logement) => {
    setSelectedLogement(logement);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDeleteLogement = async (logement: Logement) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le logement "${logement.nom_logement}" ?`)) {
      try {
        await deleteLogement(logement.id).unwrap();
        toast({
          title: "Succès",
          description: "Logement supprimé avec succès",
        });
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.data?.message || "Erreur lors de la suppression",
          variant: "destructive",
        });
      }
    }
  };

  const handleManagePhotos = (logement: Logement) => {
    setSelectedLogement(logement);
    setIsPhotoModalOpen(true);
  };

  const handleViewStats = (logement: Logement) => {
    setSelectedLogement(logement);
    setIsStatsModalOpen(true);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header avec filtres et recherche */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold">Gestion des logements</h3>
          <p className="text-muted-foreground">Consultez et gérez vos propriétés</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Rechercher un logement..."
              className="pl-8 w-full sm:w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select value={platformFilter} onValueChange={setPlatformFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Plateforme" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les plateformes</SelectItem>
              <SelectItem value="airbnb">Airbnb</SelectItem>
              <SelectItem value="booking">Booking.com</SelectItem>
            </SelectContent>
          </Select>
          
          {isAdmin && (
            <Button
              onClick={handleCreateLogement}
              className="gap-2 bg-gradient-to-r from-rent-blue-500 to-rent-purple-600 hover:from-rent-blue-600 hover:to-rent-purple-700"
            >
              <Plus className="w-4 h-4" />
              Ajouter
            </Button>
          )}
        </div>
      </div>

      {/* Affichage des logements */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">Erreur lors du chargement des logements</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {logements.length > 0 ? (
            logements.map((logement) => (
              <Card key={logement.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <Building2 className="w-5 h-5 text-rent-blue-600" />
                      <CardTitle className="text-lg">{logement.nom_logement}</CardTitle>
                    </div>
                    <Badge 
                      variant="outline"
                      className={getPlatformColor(logement.plateforme)}
                    >
                      {logement.plateforme === 'airbnb' ? 'Airbnb' : 'Booking.com'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {logement.adresse && (
                    <div className="text-sm">
                      <span className="font-medium">Adresse:</span> {logement.adresse}, {logement.ville}
                    </div>
                  )}
                  {logement.capacite && (
                    <div className="text-sm">
                      <span className="font-medium">Capacité:</span> {logement.capacite} personnes
                    </div>
                  )}
                  <div className="text-sm">
                    <span className="font-medium">Statut:</span>{' '}
                    <Badge 
                      variant={logement.statut === "Actif" ? "default" : "secondary"}
                      className={logement.statut === "Actif" ? "bg-green-100 text-green-800" : ""}
                    >
                      {logement.statut || 'Actif'}
                    </Badge>
                  </div>
                  
                  <div className="pt-2 flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewStats(logement)}
                      className="flex-1 min-w-0"
                    >
                      <BarChart3 className="w-3 h-3 mr-1" />
                      Stats
                    </Button>
                    {isAdmin && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleManagePhotos(logement)}
                          className="flex-1 min-w-0"
                        >
                          <Camera className="w-3 h-3 mr-1" />
                          Photos
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditLogement(logement)}
                          className="flex-1 min-w-0"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          Modifier
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteLogement(logement)}
                          className="flex-1 min-w-0"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          Supprimer
                        </Button>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-10">
              <p className="text-muted-foreground">Aucun logement trouvé.</p>
            </div>
          )}
        </div>
      )}

      {/* Statistiques des logements */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Répartition par plateforme</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-[#FF5A5F]"></div>
                  <span>Airbnb</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.plateforme === 'airbnb').length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-[#003580]"></div>
                  <span>Booking.com</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.plateforme === 'booking').length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Statut des logements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>Actifs</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.statut === 'Actif').length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span>En maintenance</span>
                </div>
                <span className="font-semibold">
                  {logements.filter(l => l.statut === 'Maintenance').length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Capacité totale</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {logements.reduce((sum, logement) => sum + (logement.capacite || 0), 0)}
            </div>
            <p className="text-sm text-muted-foreground">personnes au total</p>
          </CardContent>
        </Card>
      </div>

      {/* Modals */}
      <LogementModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        logement={selectedLogement}
        mode={modalMode}
      />

      <PhotoModal
        isOpen={isPhotoModalOpen}
        onClose={() => setIsPhotoModalOpen(false)}
        logement={selectedLogement}
      />

      <StatsModal
        isOpen={isStatsModalOpen}
        onClose={() => setIsStatsModalOpen(false)}
        logement={selectedLogement}
      />
    </div>
  );
};