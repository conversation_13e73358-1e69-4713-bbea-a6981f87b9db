const { Reservation, Logement } = require('../models');

// R<PERSON><PERSON>érer toutes les réservations
exports.getAllReservations = async (req, res) => {
  try {
    const reservations = await Reservation.findAll({
      include: [{
        model: Logement,
        as: 'logement',
        attributes: ['nom_logement', 'plateforme', 'adresse', 'ville']
      }],
      order: [['date_arrivee', 'DESC']]
    });

    res.json({
      success: true,
      data: reservations
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des réservations:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Récupérer une réservation par ID
exports.getReservationById = async (req, res) => {
  try {
    const { id } = req.params;

    const reservation = await Reservation.findByPk(id, {
      include: [{
        model: Logement,
        as: 'logement',
        attributes: ['nom_logement', 'plateforme', 'adresse', 'ville']
      }]
    });

    if (!reservation) {
      return res.status(404).json({
        success: false,
        message: 'Réservation non trouvée'
      });
    }

    res.json({
      success: true,
      data: reservation
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de la réservation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Créer une nouvelle réservation
exports.createReservation = async (req, res) => {
  try {
    const reservation = await Reservation.create(req.body);

    const reservationWithLogement = await Reservation.findByPk(reservation.id, {
      include: [{
        model: Logement,
        as: 'logement',
        attributes: ['nom_logement', 'plateforme', 'adresse', 'ville']
      }]
    });

    res.status(201).json({
      success: true,
      data: reservationWithLogement,
      message: 'Réservation créée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la création de la réservation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la réservation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Mettre à jour une réservation
exports.updateReservation = async (req, res) => {
  try {
    const { id } = req.params;

    const reservation = await Reservation.findByPk(id);
    if (!reservation) {
      return res.status(404).json({
        success: false,
        message: 'Réservation non trouvée'
      });
    }

    await reservation.update(req.body);

    const updatedReservation = await Reservation.findByPk(id, {
      include: [{
        model: Logement,
        as: 'logement',
        attributes: ['nom_logement', 'plateforme', 'adresse', 'ville']
      }]
    });

    res.json({
      success: true,
      data: updatedReservation,
      message: 'Réservation mise à jour avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la réservation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la réservation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Supprimer une réservation
exports.deleteReservation = async (req, res) => {
  try {
    const { id } = req.params;

    const reservation = await Reservation.findByPk(id);
    if (!reservation) {
      return res.status(404).json({
        success: false,
        message: 'Réservation non trouvée'
      });
    }

    await reservation.destroy();

    res.json({
      success: true,
      message: 'Réservation supprimée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de la réservation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la réservation',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Supprimer toutes les réservations (admin seulement)
exports.deleteAllReservations = async (req, res) => {
  try {
    console.log('DELETE /api/reservations/all endpoint hit');

    // Delete all records
    await Reservation.destroy({
      where: {},
      truncate: true
    });

    console.log('All reservations deleted successfully');

    return res.status(200).json({
      success: true,
      message: 'Toutes les réservations ont été supprimées avec succès'
    });
  } catch (error) {
    console.error('Error deleting reservations:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression des réservations',
      error: error.toString()
    });
  }
};






