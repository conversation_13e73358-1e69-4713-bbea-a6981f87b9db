
import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  LogOut,
  Upload,
  Building2,
  TrendingUp,
  DollarSign,
  Calendar,
  Users, // Used for User Management tab
  FileText,
  Download,
  Brain,
  Home,
  User,
  Shield,
  FileUp, // Used for 'Import Fichier' tab, if it was intended to be separate from 'Import'
  Bell
} from "lucide-react";
import { OverviewTab } from "@/components/dashboard/OverviewTab";
import { PropertiesTab } from "@/components/dashboard/PropertiesTab";
import { ImportTab } from "@/components/dashboard/ImportTab";
import { useToast } from "@/hooks/use-toast";
import { ThemeToggle } from "@/components/ThemeToggle";
import ConsumptionStats from "./dashboard/ConsumptionStats";
import { useNavigate } from "react-router-dom";
import { useAuth } from '../contexts/AuthContext'; // Import useAuth hook
import { IAAnalyticsTab } from "./dashboard/IAAnalyticsTab";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LogementTabSimple as LogementTab } from './dashboard/LogementTabSimple';
import { ReservationManagement } from './dashboard/ReservationManagement';
import { NotificationCenter } from './dashboard/NotificationCenter';
import { FloatingNotifications } from './dashboard/FloatingNotifications';

// Définir des interfaces pour les types de données
interface UserApiResponse {
  id: string;
  email: string;
  role: string;
}

interface ApiResponse {
  success: boolean;
  data: UserApiResponse[];
  message?: string;
  user?: UserApiResponse;
}

// --- New User Management Component ---
interface UserItem {
  id: string;
  email: string;
  role: string;
}

interface UserManagementTabProps {
  isAdmin: boolean;
}

const UserManagementTab: React.FC<UserManagementTabProps> = ({ isAdmin }) => {
  const [users, setUsers] = useState<UserItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  
  // Add state for edit user modal
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<UserItem | null>(null);
  const [editFormData, setEditFormData] = useState({
    email: '',
    role: ''
  });

  const fetchUsers = useCallback(async () => {
    if (!isAdmin) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:3000/api/auth/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json() as ApiResponse;
      
      if (data.success) {
        setUsers(data.data.map((user: UserApiResponse) => ({
          id: user.id,
          email: user.email,
          role: user.role
        })));
      } else {
        setError(data.message || 'Erreur lors de la récupération des utilisateurs');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Une erreur est survenue';
      setError(errorMessage);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleEditUser = (user: UserItem) => {
    console.log("handleEditUser called with:", user);
    setEditingUser(user);
    setEditFormData({
      email: user.email,
      role: user.role
    });
    console.log("Setting dialog open");
    setIsEditDialogOpen(true);
  };

  const handleUpdateUser = async () => {
    if (!editingUser) return;
    
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:3000/api/auth/update-user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userId: editingUser.id,
          email: editFormData.email,
          role: editFormData.role
        })
      });
      
      const data = await response.json() as ApiResponse;
      
      if (data.success && data.user) {
        // Update the user in the local state
        setUsers(prevUsers => 
          prevUsers.map(user => 
            user.id === editingUser.id 
              ? { ...user, email: data.user!.email, role: data.user!.role } 
              : user
          )
        );
        
        setIsEditDialogOpen(false);
        toast({
          title: "Succès",
          description: "Utilisateur mis à jour avec succès",
        });
      } else {
        toast({
          title: "Erreur",
          description: data.message || "Échec de la mise à jour",
          variant: "destructive",
        });
      }
    } catch (err: unknown) {
      console.error('Error updating user:', err);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour",
        variant: "destructive",
      });
    }
  };

  const handleDeleteUser = async (userId: string, userEmail: string) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur ${userEmail}?`)) {
      try {
        const token = localStorage.getItem('authToken');
        const response = await fetch('http://localhost:3000/api/auth/delete-user', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ email: userEmail })
        });
        
        const data = await response.json() as ApiResponse;
        
        if (data.success) {
          setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
          toast({
            title: "Succès",
            description: "Utilisateur supprimé avec succès",
          });
        } else {
          toast({
            title: "Erreur",
            description: data.message || "Échec de la suppression",
            variant: "destructive",
          });
        }
      } catch (err: unknown) {
        console.error('Error deleting user:', err);
        toast({
          title: "Erreur",
          description: "Une erreur est survenue lors de la suppression",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <Card className="shadow-xl rounded-2xl border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-orange-500 to-red-600 text-white p-6">
        <CardTitle className="flex items-center gap-3 text-xl font-bold">
          <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
            <Users className="w-6 h-6" />
          </div>
          👥 Gestion des Utilisateurs
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {loading ? (
          <div className="flex justify-center items-center gap-3 py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-4 border-orange-500"></div>
            <span className="text-lg font-medium text-slate-600 dark:text-slate-300">⏳ Chargement des utilisateurs...</span>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 mx-6">
              <p className="text-red-600 dark:text-red-400 font-medium">❌ {error}</p>
            </div>
          </div>
        ) : users.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-xl p-6 mx-6">
              <p className="text-slate-500 dark:text-slate-400 font-medium">👤 Aucun utilisateur trouvé.</p>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full min-w-[600px]">
              <thead className="bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 sticky top-0 z-10">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-bold text-slate-700 dark:text-slate-300 border-b-2 border-slate-300 dark:border-slate-600">
                    <div className="flex items-center gap-2">
                      <span>🆔</span>
                      <span>ID</span>
                    </div>
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-bold text-slate-700 dark:text-slate-300 border-b-2 border-slate-300 dark:border-slate-600">
                    <div className="flex items-center gap-2">
                      <span>📧</span>
                      <span>Email</span>
                    </div>
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-bold text-slate-700 dark:text-slate-300 border-b-2 border-slate-300 dark:border-slate-600">
                    <div className="flex items-center gap-2">
                      <span>👤</span>
                      <span>Rôle</span>
                    </div>
                  </th>
                  {isAdmin && (
                    <th className="px-6 py-4 text-right text-sm font-bold text-slate-700 dark:text-slate-300 border-b-2 border-slate-300 dark:border-slate-600">
                      <div className="flex items-center justify-end gap-2">
                        <span>⚙️</span>
                        <span>Actions</span>
                      </div>
                    </th>
                  )}
                </tr>
              </thead>
              <tbody>
                {users.map((user, idx) => {
                  const getRoleBadge = (role: string) => {
                    const roleConfig = {
                      'admin': { bg: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400', icon: '👑' },
                      'user': { bg: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400', icon: '👤' },
                    };
                    const config = roleConfig[role as keyof typeof roleConfig] || { bg: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400', icon: '❓' };
                    return (
                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${config.bg}`}>
                        <span>{config.icon}</span>
                        {role === 'admin' ? 'Administrateur' : 'Utilisateur'}
                      </span>
                    );
                  };

                  return (
                    <tr
                      key={user.id}
                      className={`${
                        idx % 2 === 0
                          ? "bg-white dark:bg-slate-800"
                          : "bg-slate-50 dark:bg-slate-700"
                      } hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors duration-200`}
                    >
                      <td className="px-6 py-4 text-sm font-medium text-slate-900 dark:text-slate-100 border-b border-slate-200 dark:border-slate-600">
                        <span className="bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 px-2 py-1 rounded-lg text-xs font-bold">
                          #{user.id}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-600">
                        <div className="flex items-center gap-2">
                          <span>📧</span>
                          <span className="font-medium">{user.email}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm border-b border-slate-200 dark:border-slate-600">
                        {getRoleBadge(user.role)}
                      </td>
                      {isAdmin && (
                        <td className="px-6 py-4 text-sm text-right border-b border-slate-200 dark:border-slate-600">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700 hover:text-blue-800 font-medium px-3 py-1 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                              onClick={() => {
                                console.log("Edit button clicked for user:", user);
                                handleEditUser(user);
                              }}
                            >
                              <span className="mr-1">✏️</span>
                              Modifier
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="bg-red-500 hover:bg-red-600 text-white font-medium px-3 py-1 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                              onClick={() => handleDeleteUser(user.id, user.email)}
                            >
                              <span className="mr-1">🗑️</span>
                              Supprimer
                            </Button>
                          </div>
                        </td>
                      )}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
        <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
          console.log("Dialog open state changing to:", open);
          setIsEditDialogOpen(open);
        }}>
          <DialogContent className="sm:max-w-md bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 border-0 shadow-2xl rounded-2xl">
            <DialogHeader className="bg-gradient-to-r from-orange-500 to-red-600 text-white p-6 -m-6 mb-6 rounded-t-2xl">
              <DialogTitle className="flex items-center gap-3 text-xl font-bold">
                <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Users className="w-5 h-5" />
                </div>
                ✏️ Modifier l'utilisateur
              </DialogTitle>
              <DialogDescription className="text-orange-100 mt-2">
                Modifiez les informations de l'utilisateur ci-dessous.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6 py-4">
              <div className="space-y-3">
                <Label htmlFor="email" className="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
                  <span>📧</span>
                  Email
                </Label>
                <Input
                  id="email"
                  value={editFormData.email}
                  onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                  className="bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 rounded-xl shadow-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
              <div className="space-y-3">
                <Label htmlFor="role" className="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
                  <span>👤</span>
                  Rôle
                </Label>
                <Select
                  value={editFormData.role}
                  onValueChange={(value) => setEditFormData({...editFormData, role: value})}
                >
                  <SelectTrigger className="bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 rounded-xl shadow-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <SelectValue placeholder="Sélectionner un rôle" />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl border-slate-300 dark:border-slate-600">
                    <SelectItem value="user" className="rounded-lg">
                      <div className="flex items-center gap-2">
                        <span>👤</span>
                        <span>Utilisateur</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="admin" className="rounded-lg">
                      <div className="flex items-center gap-2">
                        <span>👑</span>
                        <span>Administrateur</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter className="gap-3 pt-6">
              <Button
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                className="bg-white hover:bg-slate-50 border-slate-300 text-slate-700 font-medium px-6 py-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
              >
                ❌ Annuler
              </Button>
              <Button
                onClick={() => {
                  console.log("Save button clicked");
                  handleUpdateUser();
                }}
                className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white font-medium px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                ✅ Enregistrer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};
// --- End New User Management Component ---


export const Dashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [unreadNotifications, setUnreadNotifications] = useState(3); // Simuler des notifications non lues
  const { toast } = useToast();
  const navigate = useNavigate();

  // Get authentication state and logout function from AuthContext
  const { currentUser, userRole, logout } = useAuth();

  const isAdmin = userRole === "admin";

  // Notification de bienvenue au chargement
  useEffect(() => {
    const welcomeTimer = setTimeout(() => {
      toast({
        title: `🎉 Bienvenue ${currentUser || 'Utilisateur'} !`,
        description: `Tableau de bord ${isAdmin ? 'administrateur' : 'utilisateur'} chargé avec succès. Toutes les fonctionnalités sont disponibles.`,
      });
    }, 1000);

    // Notification des fonctionnalités disponibles
    const featuresTimer = setTimeout(() => {
      toast({
        title: "💡 Fonctionnalités disponibles",
        description: `${isAdmin ? '7 sections' : '5 sections'} disponibles : Import, Réservations, Vue d'ensemble, Statistiques, Logements${isAdmin ? ', IA, Utilisateurs' : ', IA'}`,
      });
    }, 3000);

    return () => {
      clearTimeout(welcomeTimer);
      clearTimeout(featuresTimer);
    };
  }, [currentUser, isAdmin, toast]);

  // Notifications lors du changement d'onglet
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);

    // Notifications informatives pour chaque section
    const tabNotifications = {
      'analytics': {
        title: '📥 Section Import',
        description: 'Importez vos fichiers CSV/Excel pour alimenter la base de données'
      },
      'reservations': {
        title: '📋 Section Réservations',
        description: 'Gérez vos réservations individuellement avec les outils CRUD'
      },
      'overview': {
        title: '📊 Vue d\'ensemble',
        description: 'Consultez les KPIs et statistiques générales de votre activité'
      },
      'Stat': {
        title: '📈 Statistiques',
        description: 'Analysez vos performances détaillées et tendances'
      },
      'logement': {
        title: '🏠 Logements',
        description: 'Gérez vos propriétés et consultez leurs performances'
      },
      'prediction-ia': {
        title: '🤖 Intelligence Artificielle',
        description: 'Obtenez des prédictions et optimisations basées sur vos données'
      },
      'user-management': {
        title: '👥 Gestion Utilisateurs',
        description: 'Administrez les comptes utilisateurs et leurs permissions'
      }
    };

    const notification = tabNotifications[newTab as keyof typeof tabNotifications];
    if (notification) {
      toast({
        title: notification.title,
        description: notification.description,
      });
    }
  };

  const handleLogout = () => {
    toast({
      title: "Déconnexion",
      description: "À bientôt !",
    });
    logout(); // Use the logout function from AuthContext
    // The AuthContext logout already handles localStorage.clear() and navigation to '/'
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-background border-b border-border shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-rent-blue-500 to-rent-purple-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-rent-blue-600 to-rent-purple-600 bg-clip-text text-transparent">
                RentAnalytics
              </h1>
            </div>

            <div className="flex items-center gap-4">
              {/* Bouton de notifications */}
              <div className="relative">
                <Button
                  variant="outline"
                  onClick={() => setIsNotificationOpen(!isNotificationOpen)}
                  className="gap-2 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 transition-colors relative"
                >
                  <Bell className="w-4 h-4" />
                  <span className="hidden sm:inline">Notifications</span>
                  {unreadNotifications > 0 && (
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold animate-pulse">
                      {unreadNotifications > 9 ? '9+' : unreadNotifications}
                    </div>
                  )}
                </Button>
              </div>

              <ThemeToggle />
              <div className="flex items-center gap-2 px-3 py-1 bg-muted rounded-full">
                {isAdmin ? (
                  <Shield className="w-4 h-4 text-rent-purple-600 dark:text-rent-purple-400" />
                ) : (
                  <User className="w-4 h-4 text-rent-blue-600 dark:text-rent-blue-400" />
                )}
                {/* Display user email from AuthContext */}
                <span className="text-sm font-medium text-foreground">{currentUser || "Invité"}</span>
                {isAdmin && (
                  <span className="text-xs bg-rent-purple-100 dark:bg-rent-purple-900 text-rent-purple-700 dark:text-rent-purple-300 px-2 py-0.5 rounded">
                    Admin
                  </span>
                )}
              </div>
              <Button variant="outline" onClick={handleLogout} className="gap-2">
                <LogOut className="w-4 h-4" />
                Déconnexion
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent mb-3">
                Tableau de Bord Administrateur
              </h2>
              <p className="text-muted-foreground text-lg">
                Gérez vos réservations et analysez vos performances en temps réel
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-700 border border-blue-200 dark:border-slate-600 rounded-xl px-4 py-3 shadow-sm">
                <span className="text-slate-600 dark:text-slate-300 text-sm font-medium">Dernière mise à jour</span>
                <p className="text-slate-900 dark:text-white font-semibold">{new Date().toLocaleTimeString('fr-FR')}</p>
              </div>

              {/* Indicateur de statut en temps réel */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl px-4 py-3 shadow-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-700 dark:text-green-300 text-sm font-medium">Système actif</span>
                </div>
                <p className="text-green-600 dark:text-green-400 text-xs mt-1">
                  {unreadNotifications} notifications en attente
                </p>
              </div>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-8">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-7 bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 p-2 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-600">
            {/* 1. Import */}
            <TabsTrigger
              value="analytics"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-green-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
            >
              <Upload className="w-4 h-4" />
              📥 Import
            </TabsTrigger>

            {/* 2. Réservations */}
            <TabsTrigger
              value="reservations"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
            >
              <Calendar className="w-4 h-4" />
              📋 Réservations
            </TabsTrigger>

            {/* 3. Vue d'ensemble */}
            <TabsTrigger
              value="overview"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
            >
              <TrendingUp className="w-4 h-4" />
              📊 Vue d'ensemble
            </TabsTrigger>

            {/* 4. Statistiques */}
            <TabsTrigger
              value="Stat"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-cyan-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
            >
              <BarChart3 className="w-4 h-4" />
              📈 Statistiques
            </TabsTrigger>

            {/* 5. Logements */}
            <TabsTrigger
              value="logement"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
            >
              <Building2 className="w-4 h-4" />
              🏠 Logements
            </TabsTrigger>

            {/* 6. IA */}
            <TabsTrigger
              value="prediction-ia"
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
            >
              <Brain className="w-4 h-4" />
              🤖 IA
            </TabsTrigger>

            {/* 7. Utilisateurs */}
            {isAdmin && (
              <TabsTrigger
                value="user-management"
                className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-orange-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 hover:scale-105 rounded-xl font-medium"
              >
                <Users className="w-4 h-4" />
                👥 Utilisateurs
              </TabsTrigger>
            )}
          </TabsList>


          <TabsContent value="overview" className="space-y-6">
            <OverviewTab isAdmin={isAdmin} />
          </TabsContent>

          <TabsContent value="logement" className="space-y-6">
            <LogementTab isAdmin={isAdmin} />
          </TabsContent>

          <TabsContent value="reservations" className="space-y-6">
            <ReservationManagement />
          </TabsContent>

          {isAdmin && (
            <TabsContent value="user-management" className="space-y-6">
              <UserManagementTab isAdmin={isAdmin} />
            </TabsContent>
          )}

          {isAdmin && ( /* Content for original 'Import' tab */
            <TabsContent value="import" className="space-y-6">
              <ImportTab />
            </TabsContent>
          )}

          <TabsContent value="analytics" className="space-y-6"> {/* Content for original 'Importer fichier' tab */}
            <ImportTab/> {/* Keeping ImportTab content as requested */}
          </TabsContent>

          <TabsContent value="Stat" className="space-y-6">
            <ConsumptionStats />
          </TabsContent>

          <TabsContent value="prediction-ia" className="space-y-6">
            <IAAnalyticsTab />
          </TabsContent>

          {/* If there was a distinct PropertiesTab, you'd need to re-add its trigger and content if needed.
              Based on the previous state, 'properties' was used for PredictionChart.
          */}
        </Tabs>
      </main>

      {/* Centre de notifications */}
      <NotificationCenter
        isOpen={isNotificationOpen}
        onClose={() => setIsNotificationOpen(false)}
      />

      {/* Notifications flottantes */}
      <FloatingNotifications />
    </div>
  );
};











