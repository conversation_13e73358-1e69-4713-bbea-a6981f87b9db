
import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  LogOut,
  Upload,
  Building2,
  TrendingUp,
  DollarSign,
  Calendar,
  Users, // Used for User Management tab
  FileText,
  Download,
  Brain,
  Home,
  User,
  Shield,
  FileUp // Used for 'Import Fichier' tab, if it was intended to be separate from 'Import'
} from "lucide-react";
import { OverviewTab } from "@/components/dashboard/OverviewTab";
import { PropertiesTab } from "@/components/dashboard/PropertiesTab";
import { ImportTab } from "@/components/dashboard/ImportTab";
import { useToast } from "@/hooks/use-toast";
import { ThemeToggle } from "@/components/ThemeToggle";
import ConsumptionStats from "./dashboard/ConsumptionStats";
import { useNavigate } from "react-router-dom";
import { useAuth } from '../contexts/AuthContext'; // Import useAuth hook
import PredictionChart from "./prediction/PredicitionChart"; // Assuming this path is correct
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LogementTab } from './dashboard/LogementTab';

// Définir des interfaces pour les types de données
interface UserApiResponse {
  id: string;
  email: string;
  role: string;
}

interface ApiResponse {
  success: boolean;
  data: UserApiResponse[];
  message?: string;
  user?: UserApiResponse;
}

// --- New User Management Component ---
interface UserItem {
  id: string;
  email: string;
  role: string;
}

interface UserManagementTabProps {
  isAdmin: boolean;
}

const UserManagementTab: React.FC<UserManagementTabProps> = ({ isAdmin }) => {
  const [users, setUsers] = useState<UserItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  
  // Add state for edit user modal
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<UserItem | null>(null);
  const [editFormData, setEditFormData] = useState({
    email: '',
    role: ''
  });

  const fetchUsers = useCallback(async () => {
    if (!isAdmin) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:3000/api/auth/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json() as ApiResponse;
      
      if (data.success) {
        setUsers(data.data.map((user: UserApiResponse) => ({
          id: user.id,
          email: user.email,
          role: user.role
        })));
      } else {
        setError(data.message || 'Erreur lors de la récupération des utilisateurs');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Une erreur est survenue';
      setError(errorMessage);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleEditUser = (user: UserItem) => {
    console.log("handleEditUser called with:", user);
    setEditingUser(user);
    setEditFormData({
      email: user.email,
      role: user.role
    });
    console.log("Setting dialog open");
    setIsEditDialogOpen(true);
  };

  const handleUpdateUser = async () => {
    if (!editingUser) return;
    
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:3000/api/auth/update-user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userId: editingUser.id,
          email: editFormData.email,
          role: editFormData.role
        })
      });
      
      const data = await response.json() as ApiResponse;
      
      if (data.success && data.user) {
        // Update the user in the local state
        setUsers(prevUsers => 
          prevUsers.map(user => 
            user.id === editingUser.id 
              ? { ...user, email: data.user!.email, role: data.user!.role } 
              : user
          )
        );
        
        setIsEditDialogOpen(false);
        toast({
          title: "Succès",
          description: "Utilisateur mis à jour avec succès",
        });
      } else {
        toast({
          title: "Erreur",
          description: data.message || "Échec de la mise à jour",
          variant: "destructive",
        });
      }
    } catch (err: unknown) {
      console.error('Error updating user:', err);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour",
        variant: "destructive",
      });
    }
  };

  const handleDeleteUser = async (userId: string, userEmail: string) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur ${userEmail}?`)) {
      try {
        const token = localStorage.getItem('authToken');
        const response = await fetch('http://localhost:3000/api/auth/delete-user', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ email: userEmail })
        });
        
        const data = await response.json() as ApiResponse;
        
        if (data.success) {
          setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
          toast({
            title: "Succès",
            description: "Utilisateur supprimé avec succès",
          });
        } else {
          toast({
            title: "Erreur",
            description: data.message || "Échec de la suppression",
            variant: "destructive",
          });
        }
      } catch (err: unknown) {
        console.error('Error deleting user:', err);
        toast({
          title: "Erreur",
          description: "Une erreur est survenue lors de la suppression",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <Card className="p-6 shadow-md rounded-lg">
      <CardHeader>
        <CardTitle className="text-xl font-semibold mb-4">Gestion des Utilisateurs</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-red-500 text-center py-4">{error}</div>
        ) : users.length === 0 ? (
          <p className="text-muted-foreground text-center">Aucun utilisateur trouvé.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full leading-normal table-auto">
              <thead>
                <tr className="bg-muted text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  <th className="px-5 py-3 border-b-2 border-border">ID</th>
                  <th className="px-5 py-3 border-b-2 border-border">Email</th>
                  <th className="px-5 py-3 border-b-2 border-border">Rôle</th>
                  {isAdmin && <th className="px-5 py-3 border-b-2 border-border text-right">Actions</th>}
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-accent transition-colors duration-200">
                    <td className="px-5 py-5 border-b border-border text-sm text-foreground">{user.id}</td>
                    <td className="px-5 py-5 border-b border-border text-sm text-foreground">{user.email}</td>
                    <td className="px-5 py-5 border-b border-border text-sm text-foreground capitalize">{user.role}</td>
                    {isAdmin && (
                      <td className="px-5 py-5 border-b border-border text-sm text-right whitespace-nowrap">
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2 border-blue-500 text-blue-500 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                          onClick={() => {
                            console.log("Edit button clicked for user:", user);
                            handleEditUser(user);
                          }}
                        >
                          Modifier
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          className="bg-red-500 hover:bg-red-600 text-white transition-colors"
                          onClick={() => handleDeleteUser(user.id, user.email)}
                        >
                          Supprimer
                        </Button>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
          console.log("Dialog open state changing to:", open);
          setIsEditDialogOpen(open);
        }}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Modifier l'utilisateur</DialogTitle>
              <DialogDescription>
                Modifiez les informations de l'utilisateur ci-dessous.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  value={editFormData.email}
                  onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Rôle</Label>
                <Select 
                  value={editFormData.role} 
                  onValueChange={(value) => setEditFormData({...editFormData, role: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un rôle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Annuler
              </Button>
              <Button onClick={() => {
                console.log("Save button clicked");
                handleUpdateUser();
              }}>
                Enregistrer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};
// --- End New User Management Component ---


export const Dashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const { toast } = useToast();
  const navigate = useNavigate();

  // Get authentication state and logout function from AuthContext
  const { currentUser, userRole, logout } = useAuth();

  const isAdmin = userRole === "admin";

  const handleLogout = () => {
    toast({
      title: "Déconnexion",
      description: "À bientôt !",
    });
    logout(); // Use the logout function from AuthContext
    // The AuthContext logout already handles localStorage.clear() and navigation to '/'
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-background border-b border-border shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-rent-blue-500 to-rent-purple-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-rent-blue-600 to-rent-purple-600 bg-clip-text text-transparent">
                RentAnalytics
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <ThemeToggle />
              <div className="flex items-center gap-2 px-3 py-1 bg-muted rounded-full">
                {isAdmin ? (
                  <Shield className="w-4 h-4 text-rent-purple-600 dark:text-rent-purple-400" />
                ) : (
                  <User className="w-4 h-4 text-rent-blue-600 dark:text-rent-blue-400" />
                )}
                {/* Display user email from AuthContext */}
                <span className="text-sm font-medium text-foreground">{currentUser || "Invité"}</span>
                {isAdmin && (
                  <span className="text-xs bg-rent-purple-100 dark:bg-rent-purple-900 text-rent-purple-700 dark:text-rent-purple-300 px-2 py-0.5 rounded">
                    Admin
                  </span>
                )}
              </div>
              <Button variant="outline" onClick={handleLogout} className="gap-2">
                <LogOut className="w-4 h-4" />
                Déconnexion
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            Tableau de bord
          </h2>
          <p className="text-muted-foreground">
            Vue d'overview de vos réservations et performances
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-7"> {/* Adjusted grid-cols for more tabs */}
            <TabsTrigger value="overview" className="gap-2">
              <TrendingUp className="w-4 h-4" />
              Overview
            </TabsTrigger>

            <TabsTrigger value="logement" className="gap-2">
              <Building2 className="w-4 h-4" />
              Logement
            </TabsTrigger>

            {isAdmin && (
              <TabsTrigger value="user-management" className="gap-2">
                <Users className="w-4 h-4" />
                User Management
              </TabsTrigger>
            )}

            {/* Original 'Importer fichier' tab with BarChart3 icon */}
            <TabsTrigger value="analytics" className="gap-2">
              <BarChart3 className="w-4 h-4" />
              Importer fichier
            </TabsTrigger>

            <TabsTrigger value="Stat" className="gap-2">
              Stat
            </TabsTrigger>

            <TabsTrigger value="prediction-ia" className="gap-2"> {/* Renamed from 'properties' for clarity */}
              <Brain className="w-4 h-4" />
              Prédiction IA
            </TabsTrigger>
          </TabsList>


          <TabsContent value="overview" className="space-y-6">
            <OverviewTab isAdmin={isAdmin} />
          </TabsContent>

          <TabsContent value="logement" className="space-y-6">
            <LogementTab isAdmin={isAdmin} />
          </TabsContent>

          {isAdmin && (
            <TabsContent value="user-management" className="space-y-6">
              <UserManagementTab isAdmin={isAdmin} />
            </TabsContent>
          )}

          {isAdmin && ( /* Content for original 'Import' tab */
            <TabsContent value="import" className="space-y-6">
              <ImportTab />
            </TabsContent>
          )}

          <TabsContent value="analytics" className="space-y-6"> {/* Content for original 'Importer fichier' tab */}
            <ImportTab/> {/* Keeping ImportTab content as requested */}
          </TabsContent>

          <TabsContent value="Stat" className="space-y-6">
            <ConsumptionStats />
          </TabsContent>

          <TabsContent value="prediction-ia" className="space-y-6">
            <PredictionChart />
          </TabsContent>

          {/* If there was a distinct PropertiesTab, you'd need to re-add its trigger and content if needed.
              Based on the previous state, 'properties' was used for PredictionChart.
          */}
        </Tabs>
      </main>
    </div>
  );
};











