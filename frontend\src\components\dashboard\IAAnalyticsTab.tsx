import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useGetCompleteAnalysisQuery } from '@/features/api/analyticsApi';
import {
  Brain,
  TrendingUp,
  BarChart3,
  Calendar,
  DollarSign,
  Target,
  Lightbulb,
  Activity,
  PieChart,
  LineChart as LineChartIcon,
  RefreshCw
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContaine<PERSON>,
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';

interface AnalyticsData {
  occupancyRate: {
    monthly: Array<{ month: string; rate: number; logement: string }>;
    yearly: Array<{ year: number; rate: number }>;
  };
  revenue: {
    monthly: Array<{ month: string; revenue: number; logement: string; plateforme: string }>;
    yearly: Array<{ year: number; revenue: number }>;
    comparison: Array<{ logement: string; revenue: number; occupancy: number; plateforme: string }>;
  };
  predictions: {
    pricing: Array<{ period: string; suggestedPrice: number; currentPrice: number; season: string }>;
    weakPeriods: Array<{ period: string; level: 'low' | 'medium' | 'high'; suggestions: string[] }>;
    trends: Array<{ month: string; predicted: number; confidence: number }>;
  };
  insights: {
    bestPerformers: Array<{ logement: string; metric: string; value: number }>;
    improvements: Array<{ category: string; suggestion: string; impact: string }>;
  };
}

export const IAAnalyticsTab = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [selectedLogement, setSelectedLogement] = useState('all');
  const [logements, setLogements] = useState<any[]>([]);
  const [manualRefresh, setManualRefresh] = useState(0);
  const { toast } = useToast();

  // Utiliser l'API pour récupérer les données
  const {
    data: analyticsResponse,
    isLoading: loading,
    error,
    refetch
  } = useGetCompleteAnalysisQuery({
    period: selectedPeriod,
    logement_id: selectedLogement === 'all' ? undefined : selectedLogement
  }, {
    // Notification lors du chargement des analyses
    onSuccess: (data) => {
      if (data?.data) {
        const totalRevenue = data.data.revenue?.yearly?.[1]?.revenue || 0;
        const totalReservations = data.data.occupancyRate?.monthly?.length || 0;
        toast({
          title: "🤖 Analyses IA générées",
          description: `📊 ${totalReservations} mois analysés
💰 ${totalRevenue.toLocaleString('fr-FR')}€ de revenus traités
🔮 Prédictions basées sur les données réelles`,
        });
      }
    },
    onError: (error) => {
      toast({
        title: "❌ Erreur d'analyse IA",
        description: "Impossible de générer les analyses. Vérifiez que des données sont disponibles.",
        variant: "destructive",
      });
    }
  });

  const analyticsData = analyticsResponse?.data;

  // Charger les logements disponibles
  useEffect(() => {
    const fetchLogements = async () => {
      try {
        const response = await fetch('http://localhost:3000/api/logements');
        if (response.ok) {
          const data = await response.json();
          setLogements(data.data || []);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des logements:', error);
      }
    };
    fetchLogements();
  }, []);

  // Fonction pour lancer l'analyse IA
  const runAIAnalysis = async () => {
    try {
      toast({
        title: "🤖 Analyse IA en cours",
        description: "L'intelligence artificielle analyse vos données...",
      });

      // Déclencher un nouveau fetch des données
      await refetch();
      setManualRefresh(prev => prev + 1);

      toast({
        title: "✅ Analyse terminée",
        description: "L'IA a généré des insights personnalisés pour vos logements",
      });
    } catch (error) {
      toast({
        title: "❌ Erreur",
        description: "Impossible de lancer l'analyse IA",
        variant: "destructive",
      });
    }
  };

  const COLORS = ['#FF5A5F', '#003580', '#10b981', '#f59e0b', '#8b5cf6'];

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
        <div className="space-y-2">
          <h3 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center gap-3">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-xl shadow-lg">
              <Brain className="w-6 h-6 text-white" />
            </div>
            Analyse IA Avancée
          </h3>
          <p className="text-slate-600 dark:text-slate-400 text-lg">
            Intelligence artificielle pour optimiser vos performances
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <Select
            value={selectedPeriod}
            onValueChange={(value) => {
              setSelectedPeriod(value);
              toast({
                title: "📅 Période modifiée",
                description: `Analyse mise à jour pour ${value === '3months' ? '3 mois' : value === '6months' ? '6 mois' : '1 an'}`,
              });
            }}
          >
            <SelectTrigger className="w-[180px] rounded-xl">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">3 derniers mois</SelectItem>
              <SelectItem value="6months">6 derniers mois</SelectItem>
              <SelectItem value="1year">1 an</SelectItem>
            </SelectContent>
          </Select>

          <Button
            onClick={async () => {
              toast({
                title: "🔄 Actualisation des données",
                description: "Récupération des dernières données pour l'analyse IA...",
              });

              try {
                await refetch();
                toast({
                  title: "✅ Données actualisées",
                  description: "L'analyse IA utilise maintenant les données les plus récentes",
                });
              } catch (error) {
                toast({
                  title: "❌ Erreur d'actualisation",
                  description: "Impossible de récupérer les dernières données",
                  variant: "destructive",
                });
              }
            }}
            variant="outline"
            disabled={loading}
            className="border-slate-200 dark:border-slate-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </Button>

          <Button
            onClick={runAIAnalysis}
            disabled={loading}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Analyse en cours...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                🤖 Lancer l'analyse IA
              </div>
            )}
          </Button>
        </div>
      </div>

      {!analyticsData && !loading && (
        <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
          <CardContent className="p-12 text-center">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-4 rounded-2xl shadow-lg w-fit mx-auto mb-6">
              <Brain className="w-12 h-12 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-4">
              Prêt pour l'analyse IA ?
            </h3>
            <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
              Lancez l'analyse pour obtenir des insights personnalisés sur vos performances, 
              des prédictions de revenus et des recommandations d'optimisation.
            </p>
            <Button
              onClick={runAIAnalysis}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <Brain className="w-4 h-4 mr-2" />
              Commencer l'analyse
            </Button>
          </CardContent>
        </Card>
      )}

      {analyticsData && (
        <Tabs defaultValue="performance" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 p-2 rounded-2xl shadow-lg">
            <TabsTrigger 
              value="performance" 
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600 data-[state=active]:text-white rounded-xl font-medium"
            >
              <BarChart3 className="w-4 h-4" />
              Performance
            </TabsTrigger>
            <TabsTrigger 
              value="predictions" 
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-purple-600 data-[state=active]:text-white rounded-xl font-medium"
            >
              <TrendingUp className="w-4 h-4" />
              Prédictions
            </TabsTrigger>
            <TabsTrigger 
              value="insights" 
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl font-medium"
            >
              <Lightbulb className="w-4 h-4" />
              Insights
            </TabsTrigger>
            <TabsTrigger 
              value="optimization" 
              className="gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-orange-600 data-[state=active]:text-white rounded-xl font-medium"
            >
              <Target className="w-4 h-4" />
              Optimisation
            </TabsTrigger>
          </TabsList>

          {/* Onglet Performance */}
          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Taux d'occupation */}
              <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent flex items-center gap-3">
                    <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-xl shadow-md">
                      <Activity className="w-5 h-5 text-white" />
                    </div>
                    📊 Taux d'occupation mensuel
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={analyticsData.occupancyRate.monthly}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="month" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <Tooltip
                        formatter={(value) => [`${value}%`, 'Taux d\'occupation']}
                        contentStyle={{
                          backgroundColor: '#f8fafc',
                          border: '1px solid #e2e8f0',
                          borderRadius: '12px',
                          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="rate"
                        stroke="#3b82f6"
                        fill="url(#colorOccupancy)"
                        strokeWidth={3}
                      />
                      <defs>
                        <linearGradient id="colorOccupancy" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Revenus mensuels */}
              <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent flex items-center gap-3">
                    <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl shadow-md">
                      <DollarSign className="w-5 h-5 text-white" />
                    </div>
                    💰 Revenus mensuels
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={analyticsData.revenue.monthly}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="month" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <Tooltip
                        formatter={(value) => [`${value}€`, 'Revenus']}
                        contentStyle={{
                          backgroundColor: '#f8fafc',
                          border: '1px solid #e2e8f0',
                          borderRadius: '12px',
                          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Bar
                        dataKey="revenue"
                        fill="url(#colorRevenue)"
                        radius={[8, 8, 0, 0]}
                      />
                      <defs>
                        <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#10b981" stopOpacity={0.3}/>
                        </linearGradient>
                      </defs>
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Comparaison des logements */}
            <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-xl shadow-md">
                    <BarChart3 className="w-5 h-5 text-white" />
                  </div>
                  🏆 Comparaison des performances
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {analyticsData.revenue.comparison.map((item, index) => (
                    <div key={index} className="p-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-xl">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-slate-800 dark:text-slate-200">{item.logement}</h4>
                        <Badge className={item.plateforme === 'airbnb' ? 'bg-[#FF5A5F] text-white' : 'bg-[#003580] text-white'}>
                          {item.plateforme === 'airbnb' ? '🏠 Airbnb' : '🏨 Booking'}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-slate-600 dark:text-slate-400">Revenus:</span>
                          <span className="font-bold text-emerald-600">{item.revenue.toLocaleString()}€</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-slate-600 dark:text-slate-400">Occupation:</span>
                          <span className="font-bold text-blue-600">{item.occupancy}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Onglet Prédictions */}
          <TabsContent value="predictions" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Prédictions de prix */}
              <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent flex items-center gap-3">
                    <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-xl shadow-md">
                      <DollarSign className="w-5 h-5 text-white" />
                    </div>
                    💡 Suggestions de prix optimaux
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.predictions.pricing.map((item, index) => (
                      <div key={index} className="p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-semibold text-slate-800 dark:text-slate-200">{item.period}</h4>
                          <Badge variant={item.season === 'high' ? 'default' : item.season === 'medium' ? 'secondary' : 'outline'}>
                            {item.season === 'high' ? '🔥 Haute saison' : item.season === 'medium' ? '📊 Moyenne saison' : '❄️ Basse saison'}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <div>
                            <span className="text-sm text-slate-600 dark:text-slate-400">Prix actuel: </span>
                            <span className="font-medium">{item.currentPrice}€</span>
                          </div>
                          <div>
                            <span className="text-sm text-slate-600 dark:text-slate-400">Prix suggéré: </span>
                            <span className="font-bold text-emerald-600">{item.suggestedPrice}€</span>
                          </div>
                          <div className="text-right">
                            <span className={`font-bold ${item.suggestedPrice > item.currentPrice ? 'text-emerald-600' : 'text-red-600'}`}>
                              {item.suggestedPrice > item.currentPrice ? '+' : ''}{((item.suggestedPrice - item.currentPrice) / item.currentPrice * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Tendances futures */}
              <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent flex items-center gap-3">
                    <div className="bg-gradient-to-r from-purple-500 to-blue-500 p-2 rounded-xl shadow-md">
                      <TrendingUp className="w-5 h-5 text-white" />
                    </div>
                    🔮 Prévisions des revenus
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analyticsData.predictions.trends}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="month" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <Tooltip
                        formatter={(value, name) => [
                          name === 'predicted' ? `${value}€` : `${value}%`,
                          name === 'predicted' ? 'Revenus prédits' : 'Confiance'
                        ]}
                        contentStyle={{
                          backgroundColor: '#f8fafc',
                          border: '1px solid #e2e8f0',
                          borderRadius: '12px',
                          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="predicted"
                        stroke="#8b5cf6"
                        strokeWidth={4}
                        dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                        activeDot={{ r: 8, stroke: '#8b5cf6', strokeWidth: 2 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="confidence"
                        stroke="#10b981"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                        dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Périodes faibles identifiées */}
            <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-2 rounded-xl shadow-md">
                    <Calendar className="w-5 h-5 text-white" />
                  </div>
                  ⚠️ Périodes faibles & Promotions ciblées
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {analyticsData.predictions.weakPeriods.map((period, index) => (
                    <div key={index} className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="font-semibold text-slate-800 dark:text-slate-200">{period.period}</h4>
                        <Badge variant={period.level === 'low' ? 'destructive' : 'secondary'}>
                          {period.level === 'low' ? '🔴 Critique' : '🟡 Modéré'}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm text-slate-600 dark:text-slate-400 font-medium">Suggestions d'amélioration:</p>
                        {period.suggestions.map((suggestion, idx) => (
                          <div key={idx} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                            <span className="text-sm text-slate-700 dark:text-slate-300">{suggestion}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Onglet Insights */}
          <TabsContent value="insights" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Meilleurs performers */}
              <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent flex items-center gap-3">
                    <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl shadow-md">
                      <TrendingUp className="w-5 h-5 text-white" />
                    </div>
                    🏆 Top Performers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.insights.bestPerformers.map((performer, index) => (
                      <div key={index} className="p-4 bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-xl">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-semibold text-slate-800 dark:text-slate-200">{performer.logement}</h4>
                            <p className="text-sm text-slate-600 dark:text-slate-400">Meilleur en {performer.metric}</p>
                          </div>
                          <div className="text-right">
                            <span className="text-2xl font-bold text-emerald-600">
                              {performer.metric === 'Revenus' ? `${performer.value.toLocaleString()}€` : `${performer.value}%`}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Visualisation des données */}
              <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-3">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-2 rounded-xl shadow-md">
                      <PieChart className="w-5 h-5 text-white" />
                    </div>
                    📊 Répartition des revenus
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={analyticsData.revenue.comparison}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={120}
                        dataKey="revenue"
                        label={({ logement, revenue }) => `${logement}: ${revenue.toLocaleString()}€`}
                        labelLine={false}
                        stroke="#ffffff"
                        strokeWidth={3}
                      >
                        {analyticsData.revenue.comparison.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [`${value.toLocaleString()}€`, 'Revenus']}
                        contentStyle={{
                          backgroundColor: '#f8fafc',
                          border: '1px solid #e2e8f0',
                          borderRadius: '12px',
                          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Histogramme des périodes d'occupation */}
            <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-xl shadow-md">
                    <BarChart3 className="w-5 h-5 text-white" />
                  </div>
                  📈 Histogramme des taux d'occupation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={analyticsData.occupancyRate.monthly} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                    <XAxis dataKey="month" stroke="#64748b" />
                    <YAxis stroke="#64748b" />
                    <Tooltip
                      formatter={(value) => [`${value}%`, 'Taux d\'occupation']}
                      contentStyle={{
                        backgroundColor: '#f8fafc',
                        border: '1px solid #e2e8f0',
                        borderRadius: '12px',
                        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Bar
                      dataKey="rate"
                      fill="url(#colorOccupancyBar)"
                      radius={[8, 8, 0, 0]}
                    />
                    <defs>
                      <linearGradient id="colorOccupancyBar" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#06b6d4" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#06b6d4" stopOpacity={0.3}/>
                      </linearGradient>
                    </defs>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Onglet Optimisation */}
          <TabsContent value="optimization" className="space-y-6">
            {/* Suggestions d'amélioration */}
            <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-xl shadow-md">
                    <Target className="w-5 h-5 text-white" />
                  </div>
                  🎯 Recommandations d'optimisation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {analyticsData.insights.improvements.map((improvement, index) => (
                    <div key={index} className="p-6 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl border border-orange-200 dark:border-orange-800">
                      <div className="flex items-start gap-4">
                        <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-lg shadow-md">
                          <Lightbulb className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2">{improvement.category}</h4>
                          <p className="text-slate-600 dark:text-slate-400 mb-3">{improvement.suggestion}</p>
                          <div className="flex items-center gap-2">
                            <Badge className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white">
                              💰 Impact: {improvement.impact}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Courbes de tendances */}
            <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-xl shadow-md">
                    <LineChartIcon className="w-5 h-5 text-white" />
                  </div>
                  📈 Courbes de tendances & Prévisions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={[
                    ...analyticsData.revenue.monthly.map(item => ({ ...item, type: 'historical' })),
                    ...analyticsData.predictions.trends.map(item => ({
                      month: item.month,
                      revenue: item.predicted,
                      type: 'predicted',
                      confidence: item.confidence
                    }))
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                    <XAxis dataKey="month" stroke="#64748b" />
                    <YAxis stroke="#64748b" />
                    <Tooltip
                      formatter={(value, name) => [
                        `${value}${name === 'confidence' ? '%' : '€'}`,
                        name === 'revenue' ? 'Revenus' : name === 'confidence' ? 'Confiance' : name
                      ]}
                      contentStyle={{
                        backgroundColor: '#f8fafc',
                        border: '1px solid #e2e8f0',
                        borderRadius: '12px',
                        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke="#8b5cf6"
                      strokeWidth={4}
                      dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, stroke: '#8b5cf6', strokeWidth: 2 }}
                      connectNulls={false}
                    />
                    <Line
                      type="monotone"
                      dataKey="confidence"
                      stroke="#10b981"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
                <div className="mt-4 flex flex-wrap gap-4 justify-center">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-1 bg-purple-500 rounded"></div>
                    <span className="text-sm text-slate-600 dark:text-slate-400">Revenus (Historique & Prédictions)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-1 bg-emerald-500 rounded border-dashed border border-emerald-500"></div>
                    <span className="text-sm text-slate-600 dark:text-slate-400">Niveau de confiance</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions recommandées */}
            <Card className="border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-xl shadow-md">
                    <Target className="w-5 h-5 text-white" />
                  </div>
                  ⚡ Actions immédiates recommandées
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                    <div className="text-center">
                      <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-3 rounded-xl shadow-md w-fit mx-auto mb-3">
                        <DollarSign className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2">Ajuster les prix</h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400">Augmentez de 15% pour la haute saison</p>
                    </div>
                  </div>

                  <div className="p-4 bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-xl border border-emerald-200 dark:border-emerald-800">
                    <div className="text-center">
                      <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-3 rounded-xl shadow-md w-fit mx-auto mb-3">
                        <Calendar className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2">Promotions ciblées</h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400">Lancez des offres pour janvier-février</p>
                    </div>
                  </div>

                  <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
                    <div className="text-center">
                      <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-3 rounded-xl shadow-md w-fit mx-auto mb-3">
                        <TrendingUp className="w-6 h-6 text-white" />
                      </div>
                      <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2">Optimiser marketing</h4>
                      <p className="text-sm text-slate-600 dark:text-slate-400">Focus sur les séjours longs</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};
