import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export interface Logement {
  id: number;
  nom_logement: string;
  plateforme: string;
  adresse?: string;
  ville?: string;
  capacite?: number;
  statut?: string;
  description?: string;
  photos?: string[];
  created_at?: string;
  updated_at?: string;
  total_reservations?: number;
  total_revenus?: number;
}

export interface LogementStats {
  logement: Logement;
  stats: {
    total_reservations: number;
    total_revenus: number;
    revenu_moyen: number;
    total_nuits: number;
  };
  monthlyStats: Array<{
    mois: number;
    reservations: number;
    revenus: number;
  }>;
}

export interface LogementFilters {
  plateforme?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface LogementResponse {
  success: boolean;
  data: Logement[];
  pagination: {
    total: number;
    page: number;
    pages: number;
    limit: number;
  };
}

export interface CreateLogementData {
  nom_logement: string;
  plateforme: string;
  adresse?: string;
  ville?: string;
  capacite?: number;
  description?: string;
  statut?: string;
}

export interface UpdateLogementData extends Partial<CreateLogementData> {
  id: number;
}

export const logementsApi = createApi({
  reducerPath: 'logementsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:3000/api/logements',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as any).auth?.token ?? localStorage.getItem('authToken');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    }
  }),
  tagTypes: ['Logement', 'LogementStats'],
  endpoints: (builder) => ({
    // Récupérer tous les logements
    getAllLogements: builder.query<LogementResponse, LogementFilters | void>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        if (filters.plateforme) params.append('plateforme', filters.plateforme);
        if (filters.search) params.append('search', filters.search);
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.limit) params.append('limit', filters.limit.toString());
        
        return `/?${params.toString()}`;
      },
      providesTags: ['Logement']
    }),

    // Récupérer un logement par ID
    getLogementById: builder.query<{ success: boolean; data: Logement }, number>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'Logement', id }]
    }),

    // Récupérer les statistiques d'un logement
    getLogementStats: builder.query<{ success: boolean; data: LogementStats }, { id: number; year?: number }>({
      query: ({ id, year }) => {
        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        return `/${id}/stats?${params.toString()}`;
      },
      providesTags: (result, error, { id }) => [{ type: 'LogementStats', id }]
    }),

    // Créer un nouveau logement
    createLogement: builder.mutation<{ success: boolean; data: Logement; message: string }, CreateLogementData>({
      query: (data) => ({
        url: '/',
        method: 'POST',
        body: data
      }),
      invalidatesTags: ['Logement']
    }),

    // Mettre à jour un logement
    updateLogement: builder.mutation<{ success: boolean; data: Logement; message: string }, UpdateLogementData>({
      query: ({ id, ...data }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: data
      }),
      invalidatesTags: (result, error, { id }) => [
        'Logement',
        { type: 'Logement', id },
        { type: 'LogementStats', id }
      ]
    }),

    // Supprimer un logement
    deleteLogement: builder.mutation<{ success: boolean; message: string }, number>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE'
      }),
      invalidatesTags: ['Logement']
    }),

    // Ajouter des photos à un logement
    addPhotos: builder.mutation<{ success: boolean; data: any; message: string }, { id: number; photos: FileList }>({
      query: ({ id, photos }) => {
        const formData = new FormData();
        Array.from(photos).forEach((photo) => {
          formData.append('photos', photo);
        });
        
        return {
          url: `/${id}/photos`,
          method: 'POST',
          body: formData
        };
      },
      invalidatesTags: (result, error, { id }) => [
        'Logement',
        { type: 'Logement', id }
      ]
    }),

    // Supprimer une photo d'un logement
    deletePhoto: builder.mutation<{ success: boolean; data: Logement; message: string }, { id: number; photoIndex: number }>({
      query: ({ id, photoIndex }) => ({
        url: `/${id}/photos/${photoIndex}`,
        method: 'DELETE'
      }),
      invalidatesTags: (result, error, { id }) => [
        'Logement',
        { type: 'Logement', id }
      ]
    })
  })
});

export const {
  useGetAllLogementsQuery,
  useGetLogementByIdQuery,
  useGetLogementStatsQuery,
  useCreateLogementMutation,
  useUpdateLogementMutation,
  useDeleteLogementMutation,
  useAddPhotosMutation,
  useDeletePhotoMutation
} = logementsApi;
