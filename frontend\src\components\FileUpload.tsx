import React, { useCallback, useState } from 'react';
import { Upload, FileText, AlertCircle, FileSpreadsheet } from 'lucide-react';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  isLoading: boolean;
  error: string | null;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFileSelect, isLoading, error }) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const isValidFile = (file: File): boolean => {
    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    const validExtensions = ['.csv', '.xls', '.xlsx'];
    
    return validTypes.includes(file.type) || 
           validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFile = files.find(file => isValidFile(file));
    
    if (validFile) {
      onFileSelect(validFile);
    }
  }, [onFileSelect]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && isValidFile(file)) {
      onFileSelect(file);
    }
  }, [onFileSelect]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div
        className={` 
          relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer 
          ${isDragOver 
            ? 'border-blue-500 bg-blue-50 scale-105' 
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50' 
          } 
          ${isLoading ? 'opacity-50 pointer-events-none' : ''} 
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept=".csv,.xls,.xlsx"
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={isLoading}
        />
        
        <div className="flex flex-col items-center space-y-4">
          <div className={` 
            p-4 rounded-full transition-colors duration-300 
            ${isDragOver ? 'bg-blue-100' : 'bg-gray-100'} 
          `}>
            {isLoading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            ) : (
              <div className="flex items-center space-x-2">
                <FileText className={`h-6 w-6 ${isDragOver ? 'text-blue-600' : 'text-gray-600'}`} />
                <FileSpreadsheet className={`h-6 w-6 ${isDragOver ? 'text-green-600' : 'text-gray-600'}`} />
              </div>
            )}
          </div>
          
          <div>
            <p className="text-lg font-semibold text-gray-700 mb-2">
              {isLoading ? 'Traitement en cours...' : 'Téléchargez votre fichier de données'}
            </p>
            <p className="text-sm text-gray-500">
              Glissez-déposez votre fichier CSV ou Excel ici, ou cliquez pour parcourir
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Supporte les fichiers .csv, .xls, .xlsx (Parfait pour les données Airbnb)
            </p>
          </div>
          
          {/* Supported formats visual indicator */}
          <div className="flex items-center space-x-4 pt-2">
            <div className="flex items-center space-x-1 px-3 py-1 bg-blue-100 rounded-full">
              <FileText className="h-3 w-3 text-blue-600" />
              <span className="text-xs font-medium text-blue-700">CSV</span>
            </div>
            <div className="flex items-center space-x-1 px-3 py-1 bg-green-100 rounded-full">
              <FileSpreadsheet className="h-3 w-3 text-green-600" />
              <span className="text-xs font-medium text-green-700">Excel</span>
            </div>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-3">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}
    </div>
  );
};