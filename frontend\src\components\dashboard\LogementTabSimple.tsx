import React, { useState, useEffect } from 'react';
import { Building2, Search, Plus, Edit, Trash2, Calendar, DollarSign, Users, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useGetAllLogementsQuery, useDeleteLogementMutation, Logement } from '@/features/api/logementsApi';
import { LogementModal } from './LogementModal';

// Fonction utilitaire pour formater les nombres de manière sécurisée
const formatNumber = (value: any): number => {
  if (value === null || value === undefined || value === '') return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : num;
};

export const LogementTabSimple = ({ isAdmin }: { isAdmin: boolean }) => {
  console.log('🏠 LogementTabSimple: Starting to render...');

  const [searchTerm, setSearchTerm] = useState('');
  const [platformFilter, setPlatformFilter] = useState('all');
  const [selectedLogement, setSelectedLogement] = useState<Logement | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [stats, setStats] = useState({
    totalLogements: 0,
    airbnbCount: 0,
    bookingCount: 0,
    activeCount: 0,
    maintenanceCount: 0,
    totalCapacity: 0,
    averageCapacity: 0,
    totalReservations: 0,
    totalRevenue: 0
  });

  const { toast } = useToast();

  console.log('🏠 States initialized successfully');

    // Récupérer les logements avec les filtres
    const {
      data: logementsResponse,
      isLoading: loading,
      error,
      refetch
    } = useGetAllLogementsQuery({
      search: searchTerm.trim() || undefined,
      plateforme: platformFilter === 'all' ? undefined : platformFilter
    }, {
      // Rafraîchir automatiquement toutes les 30 secondes pour capturer les nouvelles importations
      pollingInterval: 30000,
      // Refetch quand la fenêtre reprend le focus
      refetchOnFocus: true,
      // Refetch quand la connexion est rétablie
      refetchOnReconnect: true
    });

    console.log('🏠 Query hook called successfully');

    const [deleteLogement] = useDeleteLogementMutation();

    const logements = logementsResponse?.data || [];

    // Calculer les statistiques automatiquement à chaque changement de données
    useEffect(() => {
      if (logements && logements.length > 0) {
        console.log('🏠 Données logements reçues:', logements.map(l => ({
          nom: l.nom_logement,
          total_reservations: l.total_reservations,
          type: typeof l.total_reservations
        })));

        const previousCount = stats.totalLogements;
        const newStats = {
          totalLogements: logements.length,
          airbnbCount: logements.filter(l => l.plateforme === 'airbnb').length,
          bookingCount: logements.filter(l => l.plateforme === 'booking').length,
          activeCount: logements.filter(l => l.statut === 'Actif').length,
          maintenanceCount: logements.filter(l => l.statut === 'Maintenance').length,
          totalCapacity: logements.reduce((sum, l) => sum + formatNumber(l.capacite), 0),
          averageCapacity: logements.length > 0 ? Math.round(logements.reduce((sum, l) => sum + formatNumber(l.capacite), 0) / logements.length) : 0,
          totalReservations: logements.reduce((sum, l) => sum + formatNumber(l.total_reservations), 0),
          totalRevenue: logements.reduce((sum, l) => sum + formatNumber(l.total_revenus), 0)
        };

        // Notifier si de nouveaux logements ont été ajoutés (probablement via import)
        if (previousCount > 0 && newStats.totalLogements > previousCount) {
          const newLogementsCount = newStats.totalLogements - previousCount;
          toast({
            title: "🏠 Nouveaux logements détectés",
            description: `${newLogementsCount} nouveau(x) logement(s) ajouté(s) via importation`,
          });
        }

        setStats(newStats);
        console.log('🏠 Statistiques mises à jour:', newStats);
      } else {
        // Réinitialiser les stats si pas de logements
        setStats({
          totalLogements: 0,
          airbnbCount: 0,
          bookingCount: 0,
          activeCount: 0,
          maintenanceCount: 0,
          totalCapacity: 0,
          averageCapacity: 0,
          totalReservations: 0,
          totalRevenue: 0
        });
      }
    }, [logements, toast]);

    console.log('🏠 Data processed:', {
      loading,
      error: error ? 'Has error' : 'No error',
      logements: logements.length,
      response: logementsResponse ? 'Has response' : 'No response',
      stats
    });

    // Fonctions de gestion
    const getPlatformColor = (platform: string) => {
      return platform === 'airbnb' ? 'bg-[#FF5A5F] text-white' : 'bg-[#003580] text-white';
    };

    const handleCreateLogement = () => {
      setSelectedLogement(null);
      setModalMode('create');
      setIsModalOpen(true);
    };

    const handleEditLogement = (logement: Logement) => {
      setSelectedLogement(logement);
      setModalMode('edit');
      setIsModalOpen(true);
    };

    const handleDeleteLogement = async (logement: Logement) => {
      if (window.confirm(`Êtes-vous sûr de vouloir supprimer le logement "${logement.nom_logement}" ?`)) {
        try {
          await deleteLogement(logement.id).unwrap();
          toast({
            title: "Succès",
            description: "Logement supprimé avec succès",
          });
        } catch (error: any) {
          toast({
            title: "Erreur",
            description: error.data?.message || "Erreur lors de la suppression",
            variant: "destructive",
          });
        }
      }
    };

    console.log('🏠 About to render component...');

    return (
      <div className="space-y-8 animate-fade-in">
        {/* Header simple */}
        <div className="flex flex-col md:flex-row justify-between gap-6">
          <div className="space-y-2">
            <h3 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent flex items-center gap-3">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl shadow-lg">
                <Building2 className="w-6 h-6 text-white" />
              </div>
              🏠 Gestion des Logements
            </h3>
            <p className="text-slate-600 dark:text-slate-400 text-lg">
              {stats.totalLogements} logements • {formatNumber(stats.totalReservations)} réservations • {formatNumber(stats.totalRevenue).toLocaleString('fr-FR')}€ de revenus
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-500 mt-1">
              Dernière mise à jour: {new Date().toLocaleTimeString('fr-FR')} • Actualisation automatique toutes les 30s
            </p>
            {stats.totalLogements > 0 && (
              <div className="mt-2 inline-flex items-center gap-2 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 px-3 py-1 rounded-full text-xs font-medium">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                📥 Logements synchronisés avec les importations
              </div>
            )}
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
              <Input
                type="search"
                placeholder="Rechercher un logement..."
                className="pl-10 w-full sm:w-[280px] h-12 border-slate-200 dark:border-slate-700 rounded-xl shadow-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select
              value={platformFilter}
              onValueChange={(value) => setPlatformFilter(value)}
            >
              <SelectTrigger className="w-full sm:w-[200px] h-12 border-slate-200 dark:border-slate-700 rounded-xl shadow-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200">
                <SelectValue placeholder="Filtrer par plateforme" />
              </SelectTrigger>
              <SelectContent className="rounded-xl border-slate-200 dark:border-slate-700">
                <SelectItem value="all" className="rounded-lg">Toutes les plateformes</SelectItem>
                <SelectItem value="airbnb" className="rounded-lg">🏠 Airbnb</SelectItem>
                <SelectItem value="booking" className="rounded-lg">🏨 Booking.com</SelectItem>
              </SelectContent>
            </Select>

            <Button
              onClick={() => {
                refetch();
                toast({
                  title: "🔄 Actualisation",
                  description: "Liste des logements mise à jour",
                });
              }}
              variant="outline"
              className="gap-2 h-12 px-4 border-slate-200 dark:border-slate-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
            >
              <RefreshCw className="w-4 h-4" />
              Actualiser
            </Button>

            {isAdmin && (
              <Button
                onClick={handleCreateLogement}
                className="gap-2 h-12 px-6 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <Plus className="w-4 h-4" />
                Nouveau Logement
              </Button>
            )}
          </div>
        </div>

        {/* Statistiques en temps réel */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-white to-blue-50 dark:from-slate-800 dark:to-blue-900/20 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Total Logements</p>
                  <p className="text-3xl font-bold text-blue-600">{stats.totalLogements}</p>
                </div>
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-3 rounded-xl">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-white to-emerald-50 dark:from-slate-800 dark:to-emerald-900/20 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Réservations</p>
                  <p className="text-3xl font-bold text-emerald-600">{formatNumber(stats.totalReservations)}</p>
                </div>
                <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 p-3 rounded-xl">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-white to-purple-50 dark:from-slate-800 dark:to-purple-900/20 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Revenus Total</p>
                  <p className="text-3xl font-bold text-purple-600">{stats.totalRevenue.toLocaleString('fr-FR')}€</p>
                </div>
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-3 rounded-xl">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-white to-orange-50 dark:from-slate-800 dark:to-orange-900/20 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Capacité Totale</p>
                  <p className="text-3xl font-bold text-orange-600">{stats.totalCapacity}</p>
                </div>
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-3 rounded-xl">
                  <Users className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Affichage des logements */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="flex flex-col items-center gap-3">
              <div className="w-8 h-8 border-4 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="text-slate-600 dark:text-slate-400">Chargement des logements...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-64">
            <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl">
              <p className="font-medium">❌ Erreur lors du chargement des logements</p>
              <p className="text-sm mt-1">Détails: {JSON.stringify(error)}</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {logements && logements.length > 0 ? (
              logements.map((logement) => (
                <Card key={logement.id} className="group hover:shadow-2xl transition-all duration-300 hover:scale-105 border-slate-200 dark:border-slate-700 rounded-2xl overflow-hidden bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                  <CardHeader className="pb-3 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-3">
                        <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl shadow-md">
                          <Building2 className="w-4 h-4 text-white" />
                        </div>
                        <CardTitle className="text-lg font-bold text-slate-800 dark:text-slate-100 group-hover:text-emerald-600 transition-colors duration-200">
                          {logement.nom_logement || 'Logement sans nom'}
                        </CardTitle>
                      </div>
                      <Badge
                        variant="outline"
                        className={`${getPlatformColor(logement.plateforme)} font-medium px-3 py-1 rounded-full shadow-sm`}
                      >
                        {logement.plateforme === 'airbnb' ? '🏠 Airbnb' : '🏨 Booking'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4 p-6">
                    {logement.adresse && (
                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                        <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                        <span className="font-medium">📍 Adresse:</span>
                        <span>{logement.adresse}{logement.ville ? `, ${logement.ville}` : ''}</span>
                      </div>
                    )}
                    {logement.capacite && (
                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span className="font-medium">👥 Capacité:</span>
                        <span>{logement.capacite} personnes</span>
                      </div>
                    )}

                    {/* Informations d'importation */}
                    <div className="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-500 bg-slate-50 dark:bg-slate-800 p-2 rounded-lg">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span>📥 Importé automatiquement depuis fichier CSV/Excel</span>
                    </div>

                    {/* Statistiques */}
                    <div className="grid grid-cols-2 gap-3 pt-2">
                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-xl">
                        <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">Réservations</div>
                        <div className="text-lg font-bold text-blue-700 dark:text-blue-300">{logement.total_reservations || 0}</div>
                      </div>
                      <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-3 rounded-xl">
                        <div className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">Revenus</div>
                        <div className="text-lg font-bold text-emerald-700 dark:text-emerald-300">
                          {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(Number(logement.total_revenus) || 0)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Statut:</span>
                        <Badge
                          variant={logement.statut === "Actif" ? "default" : "secondary"}
                          className={`${logement.statut === "Actif"
                            ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-md"
                            : "bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-md"
                          } px-3 py-1 rounded-full font-medium`}
                        >
                          {logement.statut === "Actif" ? "✅ Actif" : "⚠️ " + (logement.statut || 'Inactif')}
                        </Badge>
                      </div>
                    </div>

                    {isAdmin && (
                      <div className="pt-4 flex gap-3 border-t border-slate-200 dark:border-slate-700">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditLogement(logement)}
                          className="flex-1 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-blue-200 text-blue-700 hover:text-blue-800 font-medium rounded-xl transition-all duration-200 hover:shadow-md"
                        >
                          <Edit className="w-3 h-3 mr-2" />
                          Modifier
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteLogement(logement)}
                          className="flex-1 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-md"
                        >
                          <Trash2 className="w-3 h-3 mr-2" />
                          Supprimer
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-2xl p-8">
                  <Building2 className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-slate-600 dark:text-slate-400 mb-2">Aucun logement trouvé</h3>
                  <p className="text-slate-500 dark:text-slate-500">
                    {searchTerm || platformFilter !== 'all' 
                      ? 'Essayez de modifier vos filtres de recherche' 
                      : 'Commencez par ajouter votre premier logement'
                    }
                  </p>
                  {isAdmin && !searchTerm && platformFilter === 'all' && (
                    <Button
                      onClick={handleCreateLogement}
                      className="mt-4 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Ajouter un logement
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Statistiques détaillées par plateforme */}
        {!loading && !error && logements && logements.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <Card className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-xl shadow-md">
                    <Building2 className="w-5 h-5 text-white" />
                  </div>
                  📊 Répartition par plateforme
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-gradient-to-r from-[#FF5A5F] to-[#FF385C] shadow-sm"></div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">🏠 Airbnb</span>
                    </div>
                    <span className="font-bold text-xl text-[#FF5A5F]">
                      {stats.airbnbCount}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-gradient-to-r from-[#003580] to-[#0057B8] shadow-sm"></div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">🏨 Booking.com</span>
                    </div>
                    <span className="font-bold text-xl text-[#003580]">
                      {stats.bookingCount}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl shadow-md">
                    <Building2 className="w-5 h-5 text-white" />
                  </div>
                  ✅ Statut des logements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 shadow-sm"></div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">✅ Actifs</span>
                    </div>
                    <span className="font-bold text-xl text-green-600">
                      {stats.activeCount}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 shadow-sm"></div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">⚠️ En maintenance</span>
                    </div>
                    <span className="font-bold text-xl text-yellow-600">
                      {stats.maintenanceCount}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 border-slate-200 dark:border-slate-700 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent flex items-center gap-3">
                  <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-xl shadow-md">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  👥 Analyse de capacité
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  <div>
                    <div className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2">
                      {stats.totalCapacity}
                    </div>
                    <p className="text-sm font-medium text-slate-600 dark:text-slate-400">👥 personnes au total</p>
                  </div>
                  <div className="p-3 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Moyenne: <span className="font-bold text-blue-600">
                        {stats.averageCapacity}
                      </span> personnes/logement
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Modal */}
        <LogementModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          logement={selectedLogement}
          mode={modalMode}
        />
      </div>
    );
};
